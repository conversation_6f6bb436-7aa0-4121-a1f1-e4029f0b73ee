# 子应用跳弹窗子应用文档

## 主要改动

### 1. 新增openmicrodialog方法
```javascript
// A子应用打开B子应用弹窗
const openmicrodialog = window.$wujie.props.openmicrodialog

openmicrodialog(
  {
    microname: 'D04M08B1',
    viewtype: 'ED'
  },
  {
    lsp: {
      orgtype: 'D01M03B1',
      orgbillid: this.idx,
      orglifestate: 0
    }
  },
)
```
**改动说明：**
- 参数1
  - microname 打开另一个子应用的名称
  - viewtype  打开子应用的视图类型  1.编辑窗体edit → ed  2.选择窗体select → st  3.引用窗体cite → ce
- 参数2        为传递的自定义参数 
  - orgtype    原始订单来源编码 如'D01M03B1'
  - orgbillid   原始订单ID
  - orglifestate 组件状态码设定 0:弹窗页面  1:铆钉页面

### 2. 新增接受props数据信息
```javascript
Vue.prototype.$ismicroapp = window.__POWERED_BY_WUJIE__

// B子应用接收参数
if (this.$ismicroapp) {
  const microprops = window.$wujie.props.dialogmicroprops || {}
  console.log('all props:', microprops)
  // A子应用传递的参数,根据字段直接获取即可
  const lsp = microprops.lsp || {}
  
  // 提供关闭弹窗的方法
  // microprops.closedialog()
  
  // 提供子应用信息
  // microprops.microinfo
}
```
**改动说明：**
- microinfo  返回当前子应用的基础信息
- closedialog 提供关闭弹窗的方法
- [lsp]       根据A子应用传递的参数,根据字段直接获取即可
