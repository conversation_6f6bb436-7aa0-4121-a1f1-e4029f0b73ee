{"name": "vue3-element-template", "version": "2.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint --cache \"src/**/*.{vue,ts}\" --fix", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky", "commit": "git add . & git-cz", "postinstall": "node scripts/remove-empty-sourcemaps.cjs"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write"], "*.{scss,css}": ["prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@easytable/vue": "0.0.6", "@element-plus/icons-vue": "2.3.1", "@vueuse/core": "12.8.2", "@wangeditor-next/editor": "5.6.34", "@wangeditor-next/editor-for-vue": "5.1.14", "animate.css": "4.1.1", "axios": "1.8.4", "default-passive-events": "2.0.0", "driver.js": "0.9.8", "echarts": "5.6.0", "element-plus": "2.9.8", "file-saver": "^2.0.5", "js-cookie": "2.2.0", "js-pinyin": "^0.1.9", "jszip": "^3.10.1", "lodash-es": "4.17.21", "lrz": "4.9.41", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "8.2.0", "pinia": "3.0.2", "qrcodejs2-fix": "^0.0.1", "qs": "6.14.0", "vue": "3.5.13", "vue-i18n": "11.1.11", "vue-router": "4.5.0", "wujie-vue3": "1.0.28"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "@eslint/js": "9.25.0", "@iconify/utils": "2.3.0", "@types/codemirror": "5.60.15", "@types/lodash-es": "4.17.12", "@types/node": "22.14.1", "@types/nprogress": "0.2.3", "@types/path-browserify": "1.0.3", "@types/qs": "6.9.18", "@types/sortablejs": "1.15.8", "@typescript-eslint/eslint-plugin": "8.26.1", "@typescript-eslint/parser": "8.30.1", "@vitejs/plugin-vue": "5.2.1", "autoprefixer": "10.4.21", "commitizen": "4.3.1", "cz-git": "1.11.1", "eslint": "9.25.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-vue": "10.0.0", "globals": "15.15.0", "husky": "9.1.7", "lint-staged": "15.5.1", "postcss": "8.5.3", "postcss-html": "1.8.0", "postcss-scss": "4.0.9", "prettier": "3.5.3", "sass": "1.86.3", "stylelint": "16.18.0", "stylelint-config-html": "1.1.0", "stylelint-config-recess-order": "6.0.0", "stylelint-config-recommended": "15.0.0", "stylelint-config-recommended-scss": "14.1.0", "stylelint-config-recommended-vue": "1.6.0", "stylelint-prettier": "5.0.3", "terser": "5.39.0", "typescript": "5.8.3", "typescript-eslint": "8.30.1", "unocss": "65.4.3", "unplugin-auto-import": "19.1.2", "unplugin-vue-components": "28.5.0", "vite": "6.3.2", "vite-plugin-mock-dev-server": "1.8.5", "vite-plugin-svg-icons": "2.0.1", "vue-eslint-parser": "10.1.3", "vue-tsc": "2.2.8"}, "engines": {"node": ">=18.0.0"}, "pnpm": {"overrides": {"@floating-ui/dom": "1.4.1", "@floating-ui/core": "1.4.1", "@floating-ui/utils": "0.2.9"}}, "repository": "https://gitee.com/youlaiorg/vue3-element-admin.git", "author": "有来开源组织", "license": "MIT"}