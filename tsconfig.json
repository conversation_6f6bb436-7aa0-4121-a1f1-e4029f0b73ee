{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },

    // 严格性和类型检查相关配置
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,

    // 模块和兼容性相关配置
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,

    // 调试和兼容性相关配置
    "sourceMap": true,
    "useDefineForClassFields": true,
    "allowJs": true,

    // 类型声明相关配置
    "types": ["node", "vite/client", "element-plus/global"]
  },

  "include": ["mock/**/*.ts", "src/**/*.ts", "src/**/*.vue", "vite.config.ts"],
  "exclude": ["node_modules", "dist"]
}
