import type { NavigationGuardNext, RouteLocationNormalized, RouteRecordRaw } from "vue-router";
import NProgress from "@/utils/nprogress";
import { getAccessToken } from "@/utils/auth";
import router from "@/router";
import { usePermissionStore, useUserStore } from "@/store";
import {
  cacheMicroApps,
  generateUnFoundRoutes,
  setupMicroApps,
  preloadMicroApp,
  generateAdminRoutes,
  manualRegisterMicro,
} from "@/micro/register";
import { setDocumentTitle } from "@/micro";

let dynamicRoutesNames: string[] = [];

export function getDynamicNames() {
  return dynamicRoutesNames;
}

export function setDynamicNames(arr: string[]) {
  dynamicRoutesNames = arr;
}

export function setupPermission() {
  // 白名单路由
  const whiteList = ["/login"];

  router.beforeEach(async (to, from, next) => {
    NProgress.start();

    const isLogin = !!getAccessToken(); // 判断是否登录
    if (isLogin) {
      if (to.path === "/login") {
        // 已登录，访问登录页，跳转到首页
        next({ path: "/" });
      } else {
        const permissionStore = usePermissionStore();
        // 判断路由是否加载完成
        if (permissionStore.isRoutesLoaded) {
          if (to.matched.length === 0) {
            // 路由未匹配，跳转到404
            next("/404");
          } else {
            // 动态设置页面标题
            const title = (to.params.title as string) || (to.query.title as string);
            if (title) {
              to.meta.title = title;
            }
            // 缓存micro应用
            cacheMicroApps(to);
            next();
          }
        } else {
          try {
            // 生成动态路由
            const dynamicRoutes = await permissionStore.generateRoutes();
            console.log("dynamicRoutes", "dynamicRoutes", dynamicRoutes);
            const pageRoutes = dynamicRoutes.filter((item: any) => item.path === "/");
            const btnRoutes = dynamicRoutes.filter((item: any) => item.path !== "/");
            pageRoutes.forEach((route: RouteRecordRaw) => {
              router.addRoute(route);
              dynamicRoutesNames.push(route.name);
            });
            btnRoutes.forEach((route: RouteRecordRaw) => {
              if (route.path.at(0) === "/") {
                route.path = route.path.substring(1);
              }
              router.addRoute("/", route);
              dynamicRoutesNames.push(route.name);
            });
            const allRoutes = router.getRoutes();
            console.log("allRoutes", allRoutes);
            const microRoutes = allRoutes
              .filter((item: any) => {
                const { ismicroapp, microappname } = item.meta || {};
                return ismicroapp && microappname;
              })
              .map((route) => route.meta || {});
            console.log("子应用列表", microRoutes);
            setupMicroApps(microRoutes);
            manualRegisterMicro();
            console.log("正在预加载微应用...");
            if (!import.meta.env.DEV) {
              preloadMicroApp(5);
            }
            // 404/401等路由需要最后注册
            generateUnFoundRoutes();
            // 管理员路由
            generateAdminRoutes();
            // 缓存micro应用
            cacheMicroApps(to);
            next({ ...to, replace: true });
          } catch (error) {
            console.error(error);
            // 路由加载失败，重置 token 并重定向到登录页
            await useUserStore().clearSessionAndCache();
            redirectToLogin(to, next);
            NProgress.done();
          }
        }
      }
    } else {
      // 未登录，判断是否在白名单中
      if (whiteList.includes(to.path)) {
        next();
      } else {
        // 不在白名单，重定向到登录页
        redirectToLogin(to, next);
        NProgress.done();
      }
    }
  });

  // 后置守卫，保证每次路由跳转结束时关闭进度条
  router.afterEach((route) => {
    NProgress.done();
    if (route?.meta?.title) {
      setDocumentTitle(route.meta.title);
    }
  });
}

// 重定向到登录页
function redirectToLogin(to: RouteLocationNormalized, next: NavigationGuardNext) {
  const params = new URLSearchParams(to.query as Record<string, string>);
  const queryString = params.toString();
  const redirect = queryString ? `${to.path}?${queryString}` : to.path;
  next(`/login?redirect=${encodeURIComponent(redirect)}`);
}

/** 判断是否有权限 */
export function hasAuth(value: string | string[], type: "button" | "role" = "button") {
  const { roles, perms } = useUserStore().userInfo;

  // 超级管理员 拥有所有权限
  if (type === "button" && roles.includes("ROOT")) {
    return true;
  }

  const auths = type === "button" ? perms : roles;
  return typeof value === "string"
    ? auths.includes(value)
    : value.some((perm) => auths.includes(perm));
}
