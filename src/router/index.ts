/**
 * @file index.ts
 * @description 路由导航
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-24 caoshi 添加/:pathMatch(.*)*解决大量动态路由注册无文件浏览器警告
 * 2025-04-25 caoshi 路由改为默认进登录页、增加omsRoutes
 * 2025-04-26 caoshi 动态路由删除omsRoutes，增加测试页
 */

import type { App } from "vue";
import { createRouter, createWebHistory, type RouteRecordRaw } from "vue-router";
export const Layout = () => import("@/layout/index.vue");
// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: { hidden: true },
  },
  {
    path: "/loginwxe",
    component: () => import("@/views/login/indexwxe.vue"),
    meta: { hidden: true },
  },
  {
    path: "/logindd",
    component: () => import("@/views/login/indexdd.vue"),
    meta: { hidden: true },
  },
  {
    path: "/loginrtk",
    component: () => import("@/views/login/indexrtk.vue"),
    meta: { hidden: true },
  },
  {
    path: "/",
    name: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        name: "Dashboard",
        meta: {
          title: "首页",
          icon: "homepage",
          affix: true,
          keepAlive: true,
        },
      },
      {
        path: "profile",
        name: "Profile",
        component: () => import("@/views/profile/index.vue"),
        meta: { title: "个人中心", hidden: true, keepAlive: true },
      },
      {
        path: "loginlog",
        name: "LoginLog",
        component: () => import("@/views/modules/SYS/SYSM08B1/index.vue"),
        meta: { title: "登录日志", hidden: true, keepAlive: true },
      },
      {
        path: "settinglog",
        name: "SettingLog",
        component: () => import("@/views/modules/SYS/SYSM08B2/index.vue"),
        meta: { title: "操作日志", hidden: true, keepAlive: true },
      },
      {
        path: "SYS/SYSM10/B2",
        name: "SYSM10B2",
        component: () => import("@/views/wujie/index.vue"),
        meta: { title: "订单", hidden: true, sync: true },
      },
      {
        path: "SYS/SYSM10/B3",
        name: "SYSM10B3",
        component: () => import("@/views/wujie/index.vue"),
        meta: { title: "发票", hidden: true, sync: true },
      },
      {
        path: "SYS/SYSM02/B2",
        name: "SYSM02B2",
        component: () => import("@/views/wujie/index.vue"),
        meta: { title: "续费", hidden: true, sync: true },
      },
    ],
  },
];

export const unFoundRoutes = [
  {
    path: "401",
    component: () => import("@/views/error/401.vue"),
    meta: { hidden: true },
  },
  {
    path: "404",
    component: () => import("@/views/error/404.vue"),
    meta: { hidden: true },
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("@/views/error/404.vue"),
  },
];

export const adminRoute = [
  {
    path: "microstate",
    name: "MicroState",
    component: () => import("@/views/micro/state/index.vue"),
    meta: { title: "微应用管理", hidden: true, keepAlive: true },
  },
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// 全局注册 router
export function setupRouter(app: App<Element>) {
  app.use(router);
}

export default router;
