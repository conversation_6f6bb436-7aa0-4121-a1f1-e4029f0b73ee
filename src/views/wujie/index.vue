<script setup lang="ts">
import WujieVue from "wujie-vue3";
import { useRoute } from "vue-router";
import { urlKeyMap } from "@/micro/register";
import { lifecycles, plugins, wujieProps } from "@/micro";
const route = computed(() => useRoute());
let url = ref("");
let name = ref("");
const getPath = () => {
  const path = route.value.path;
  const split = path.split("/");
  split.shift();
  if (urlKeyMap.has(split[0])) {
    const u = urlKeyMap.get(split[0])[0];
    name.value = u;
    url.value = u;
    for (let i = 0; i < split.length; i++) {
      url.value += `/${split[i]}`;
    }
  }
};
watch(
  () => route,
  () => {
    getPath();
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<template>
  <!--单例模式，name相同则复用一个无界实例，改变url则子应用重新渲染实例到对应路由 -->
  <WujieVue
    v-if="name && url"
    :key="url + name"
    width="100%"
    height="100%"
    :url="url"
    :name="name"
    :props="wujieProps"
    :plugins="plugins"
    :alive="false"
    :sync="true"
    :beforeLoad="lifecycles.beforeLoad"
    :beforeMount="lifecycles.beforeMount"
    :afterMount="lifecycles.afterMount"
    :beforeUnmount="lifecycles.beforeUnmount"
    :afterUnmount="lifecycles.afterUnmount"
    :activated="lifecycles.activated"
    :deactivated="lifecycles.deactivated"
    :loadError="lifecycles.loadError"
  ></WujieVue>
</template>

<style scoped lang="scss"></style>
