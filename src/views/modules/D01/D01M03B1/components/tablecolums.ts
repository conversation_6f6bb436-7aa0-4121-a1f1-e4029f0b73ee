export const tableTh = {
  formcode: "D01M03B1Th",
  item: [
    {
      itemcode: "refno",
      itemname: "单据编号",
      minwidth: "150",
      defwidth: "",
      displaymark: 1,
      fixed: 1,
      sortable: 1,
      overflow: 1,
      aligntype: "center",
      datasheet: "Bus_Machining.refno",
    },
    {
      itemcode: "billtype",
      itemname: "单据类型",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_Machining.billtype",
    },
    {
      itemcode: "billtitle",
      itemname: "单据标题",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_Machining.billtitle",
    },
    {
      itemcode: "billdate",
      itemname: "单据日期",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_Machining.billdate",
    },
    {
      itemcode: "custorderid",
      itemname: "客户订单号",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_Machining.custorderid",
    },
    {
      itemcode: "groupuid",
      itemname: "客户编码",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "App_Workgroup.groupuid",
    },
    {
      itemcode: "groupname",
      itemname: "客户",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "App_Workgroup.groupname",
    },
    {
      itemcode: "moneyname",
      itemname: "币种",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_Machining.moneyname",
    },
    {
      itemcode: "billtaxamount",
      itemname: "总金额",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_Machining.billtaxamount",
    },
    {
      itemcode: "advaamount",
      itemname: "预收款",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_Machining.advaamount",
    },
    {
      itemcode: "balance",
      itemname: "结余",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "billcostbudgetamt",
      itemname: "成本预算",
      minwidth: "80",
      displaymark: 0,
      overflow: 1,
    },
    {
      itemcode: "billplandate",
      itemname: "计划时间",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_Machining.billplandate",
    },
    {
      itemcode: "summary",
      itemname: "摘要",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_Machining.summary",
    },
    {
      itemcode: "salesman",
      itemname: "业务员",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_Machining.salesman",
    },
    {
      itemcode: "billwkwpname",
      itemname: "最新工序",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_Machining.billwkwpname",
    },
    {
      itemcode: "status",
      itemname: "状态",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_Machining.status",
    },
    {
      itemcode: "itemcount",
      itemname: "款数",
      minwidth: "60",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_Machining.itemcount",
    },
    {
      itemcode: "printcount",
      itemname: "打印次数",
      minwidth: "100",
      displaymark: 0,
      overflow: 1,
    },
    {
      itemcode: "lister",
      itemname: "制表",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_Machining.lister",
    },
    {
      itemcode: "assessor",
      itemname: "审核员",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      sortable: 1,
      datasheet: "Bus_Machining.assessor",
    },
    {
      itemcode: "amtstatus",
      itemname: "收款状态",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
    },
  ],
};

export const tableList = {
  formcode: "D01M03B1List",
  item: [
    {
      itemcode: "refno",
      itemname: "单据编号",
      minwidth: "150",
      defwidth: "",
      displaymark: 1,
      overflow: 1,
      fixed: 1,
      sortable: 1,
      aligntype: "center",
      datasheet: "Bus_Machining.refno",
    },
    {
      itemcode: "billtype",
      itemname: "单据类型",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_Machining.billtype",
    },
    {
      itemcode: "billdate",
      itemname: "单据日期",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_Machining.billdate",
    },
    {
      itemcode: "custorderid",
      itemname: "客户订单号",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_Machining.custorderid",
    },
    {
      itemcode: "groupname",
      itemname: "客户",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "App_Workgroup.groupname",
    },
    {
      itemcode: "goodsuid",
      itemname: "货品编码",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsuid",
    },
    {
      itemcode: "goodsname",
      itemname: "货品名称",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsname",
    },
    {
      itemcode: "goodsunit",
      itemname: "单位",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsunit",
    },
    {
      itemcode: "goodsspec",
      itemname: "货品规格",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.goodsspec",
    },
    {
      itemcode: "partid",
      itemname: "外部编码",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "Mat_Goods.partid",
    },
    {
      itemcode: "quantity",
      itemname: "数量",
      minwidth: "80",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_MachiningItem.quantity",
    },
    {
      itemcode: "taxprice",
      itemname: "单价",
      minwidth: "80",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_MachiningItem.taxprice",
    },
    {
      itemcode: "taxamount",
      itemname: "金额",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_MachiningItem.taxamount",
    },
    {
      itemcode: "finishqty",
      itemname: "完成数",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_MachiningItem.finishqty",
    },
    {
      itemcode: "remainder",
      itemname: "结余数",
      minwidth: "100",
      displaymark: 0,
      overflow: 1,
    },
    {
      itemcode: "buyquantity",
      itemname: "采购数",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_MachiningItem.buyquantity",
    },
    {
      itemcode: "wkwpname",
      itemname: "工序",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "status",
      itemname: "状态",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "itemplandate",
      itemname: "评审交期",
      minwidth: "100",
      displaymark: 1,
      sortable: 1,
      overflow: 1,
      datasheet: "Bus_MachiningItem.itemplandate",
    },
    {
      itemcode: "remark",
      itemname: "备注",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "Bus_MachiningItem.remark",
    },
  ],
};
export const tableItem = {
  formcode: "D01M03B1Item",
  item: [
    {
      itemcode: "goodsuid",
      itemname: "货品编码",
      minwidth: "120",
      defwidth: "",
      displaymark: 1,
      fixed: 1,
      sortable: 0,
      overflow: 1,
      aligntype: "center",
      operationmark: 1,
    },
    {
      itemcode: "goodsname",
      itemname: "货品名称",
      minwidth: "120",
      displaymark: 1,
      fixed: 1,
      overflow: 1,
      operationmark: 1,
    },
    {
      itemcode: "goodsspec",
      itemname: "规格",
      minwidth: "200",
      displaymark: 1,
      fixed: 1,
      overflow: 1,
      operationmark: 1,
    },
    {
      itemcode: "goodsunit",
      itemname: "单位",
      minwidth: "120",
      displaymark: 1,
      fixed: 1,
      overflow: 1,
      operationmark: 1,
    },
    {
      itemcode: "partid",
      itemname: "外部编码",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      operationmark: 1,
    },
    {
      itemcode: "quantity",
      itemname: "数量",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "price",
      itemname: "未税单价",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "amount",
      itemname: "未税金额",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "itemtaxrate",
      itemname: "税率",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "taxprice",
      itemname: "含税单价",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "taxamount",
      itemname: "含税金额",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "itemorgdate",
      itemname: "原始交期",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "itemplandate",
      itemname: "评审交期",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "remark",
      itemname: "备注",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "ordercostuid",
      itemname: "核价单号",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "stoqty",
      itemname: "库存发货",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "maxqty",
      itemname: "最大发货数",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      editmark: 1,
    },
    {
      itemcode: "wkqty",
      itemname: "生产需求",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "wkquantity",
      itemname: "生产数",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "buyquantity",
      itemname: "采购数",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "bomtype",
      itemname: "bom来源",
      minwidth: "100",
      displaymark: 0,
      overflow: 1,
    },
    {
      itemcode: "status",
      itemname: "状态",
      minwidth: "80",
      displaymark: 0,
      overflow: 1,
    },
    {
      itemcode: "finishqty",
      itemname: "发货数",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
    },
    {
      itemcode: "outquantity",
      itemname: "出库数",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
    },
  ],
};
