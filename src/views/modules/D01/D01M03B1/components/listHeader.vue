<template>
  <div>
    <div class="filter-container flex j-s a-c">
      <div style="position: absolute; left: 20px">
        <el-input
          v-model="strfilter"
          placeholder="请输入查询"
          prefix-icon="Search"
          style="width: 260px; height: 100%"
          class="filter-item"
          size="default"
          clearable
          @clear="$emit('btnSearch', strfilter)"
          @keyup.enter.native="$emit('btnSearch', strfilter)"
        >
          <template #append>
            <el-button size="default" @click="$emit('btnSearch', strfilter)">搜索</el-button>
          </template>
        </el-input>

        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="primary"
          icon="plus"
          plain
          size="default"
          @click="$emit('btnAdd')"
        >
          添加
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="danger"
          plain
          size="default"
          @click="$emit('btnDel')"
        >
          删除
        </el-button>
      </div>
      <div class="iShowBtn" style="position: absolute; right: 0%">
        <div style="display: inline-block">
          <el-switch
            v-model="thorList"
            style="
              --el-switch-on-color: #4080ff;
              --el-switch-off-color: #13ce66;
              margin-right: 10px;
            "
            @change="changeModelUrl"
          />
          <span style="margin-right: 10px; font-size: 14px">{{ thorList ? "单据" : "明细" }}</span>
        </div>
        <el-checkbox
          v-model="balance"
          label="结存"
          border
          size="small"
          style="margin-right: 10px"
          @change="changeBalance"
        />
        <el-button
          size="default"
          icon="search"
          :type="'default'"
          title="高级筛选"
          @click="openSearchForm()"
        />
        <el-button size="default" icon="refresh-right" title="刷新" @click="$emit('bindData')" />
        <el-button size="default" icon="download" title="导出Excel" @click="$emit('btnExport')" />
        <el-button size="default" icon="tools" title="列设置" @click="openDialog()" />
        <el-button size="default" icon="HelpFilled" title="帮助" @click="$emit('btnHelp')" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
const thorList = ref(true);
const balance = ref(false);
const strfilter = ref(""); //搜索内容
// const searchForm = ref();
const searchVisible = ref(false);

function openDialog() {}
function changeModelUrl() {}
function changeBalance() {}

defineProps({
  tableForm: {
    type: Object,
  },
});
async function openSearchForm() {
  searchVisible.value = true;
  // await nextTick(() => {
  //   searchForm.value.getInit();
  // });
}
</script>
<style lang="scss" scoped></style>
