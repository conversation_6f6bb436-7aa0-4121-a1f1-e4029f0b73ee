<script setup lang="ts">
import D01M01B1ListHeader from "./components/listHeader.vue";
import D01M01B1TableList from "./components/tableList.vue";
import D01M01B1FormEdit from "./components/formEdit.vue";
import { ref } from "vue";
const showhelp = ref(false);
const formVisible = ref(false);
const tableList = ref<InstanceType<typeof D01M01B1TableList> | null>(null);
const helpmodel = ref(null);
const idx = ref("0");
const tableForm = ref<Object>({});
function showForm(val: string) {
  idx.value = val;
  formVisible.value = true;
}
function deleteRows() {
  tableList.value?.deleteRows();
}

function btnSearch(val: any) {
  if (tableList.value) {
    tableList.value.search(val);
  }
}
function btnHelp() {
  showhelp.value = !showhelp.value;
  if (showhelp.value && helpmodel.value) {
    // helpmodel.value.bindData();
  }
}
function bindData() {
  if (tableList.value) {
    tableList.value.bindData();
  }
}
function sendTableForm(data: any) {
  tableForm.value = data;
}
function changeIdx(val: any) {
  idx.value = val;
}
function closeForm() {
  formVisible.value = false;
  bindData();
}
onMounted(() => {
  if (tableList.value) {
    bindData();
    tableList.value.getColumn();
  }
});
</script>

<template>
  <div class="page-container">
    <div v-if="formVisible">
      <D01M01B1FormEdit
        ref="formedit"
        :idx="idx"
        @changeIdx="changeIdx"
        @bindData="bindData"
        @closeForm="closeForm"
      />
    </div>
    <!-- 页头区域 -->
    <div v-show="!formVisible" class="header-container">
      <D01M01B1ListHeader
        ref="listHeader"
        @btnAdd="() => showForm('0')"
        @btnDel="deleteRows"
        @btnSearch="btnSearch"
        @btnHelp="btnHelp"
        @bindData="bindData"
      />
      <!-- 表格内容区域 -->
      <div class="content-container">
        <el-row>
          <el-col :span="showhelp ? 20 : 24">
            <D01M01B1TableList
              ref="tableList"
              @showForm="showForm"
              @sendTableForm="sendTableForm"
            />
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  height: 90vh;
  overflow: hidden;
}

.header-container {
  z-index: 10;
  flex: 0 0 auto;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.content-container {
  flex: 1;
  padding: 0 0;
  padding-top: 35px;
  overflow: auto;
  background-color: #f9f9f9;
}
</style>
