<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
// import { ElMessage, ElMessageBox } from "element-plus";
// import request from "@/utils/request";
import EditGroupBtns from "@/components/GroupBtns/index.vue";
import { operateBar, processBar } from "./formTemp/operate";
import D01M01B1EditHeader from "./formTemp/editHeader.vue";
import FormTemp from "@/components/FormTemp/index.vue";
import { formTempComp } from "./formTemp/index.js";
import request from "@/utils/request";

interface FormData {
  groupuid: string;
  groupname: string;
  seller: string;
  remark: string;
  enabledmark: number;
  rownum: number;
  grouptype: string;
  lister: string;
  createby: string;
  id?: number;
}
const editGroupBtns = ref();
const formtemplate = ref(formTempComp);

const props = defineProps<{
  idx: string;
}>();

const emit = defineEmits<{
  (_e: "changeIdx", _id: number): void;
  (_e: "bindData"): void;
  (_e: "closeForm"): void;
}>();

const formdata = ref<FormData>({
  groupuid: "",
  groupname: "",
  seller: "",
  remark: "",
  enabledmark: 1,
  rownum: 0,
  grouptype: "客户信息",
  lister: JSON.parse(localStorage.getItem("getInfo") || "{}").realname,
  createby: JSON.parse(localStorage.getItem("getInfo") || "{}").realname,
});

const title = "基本信息";
const formstate = ref(0);
const submitting = ref(0);

const formcontainHeight = computed(() => `${window.innerHeight - 123}px`);

const bindData = async () => {
  formstate.value = 0;
  if (props.idx !== "0") {
    try {
      const res = await request.get(`/sale/D01M01B1/getEntity?key=${props.idx}`);
      if (res.data.code === 200) {
        formdata.value = res.data.data;
        formstate.value = formdata.value.id ? 1 : 0;
      } else {
        ElMessageBox.alert(`${res.data.code} 读取异常`, "错误", {
          confirmButtonText: "确定",
          type: "warning",
          callback: () => emit("closeForm"),
        });
      }
    } catch {
      ElMessage.error("请求错误");
    }
  }
};
const getNextCode = async () => {
  try {
    const res = await request.get("/sale/D01M01B1/getNextCode?type=1");
    if (res.data.code === 200) {
      formdata.value.groupuid = res.data.data;
    } else {
      ElMessage.warning("获取编码失败");
    }
  } catch (error) {
    console.error("Failed to fetch next code:", error);
  }
};
const formHeader = ref();
const formtemp = ref();
const submitForm = () => {
  const validateRule =
    formHeader.value?.$refs?.formdata || formtemp.value?.$refs?.formHeader?.$refs?.formdata;
  validateRule?.validate((valid: boolean) => {
    if (valid) {
      saveForm();
    }
  });
};

const saveForm = async () => {
  // submitting.value = 1;
  const param = { ...formdata.value };
  try {
    const res =
      props.idx === "0"
        ? await request.post("/sale/D01M01B1/create", JSON.stringify(param))
        : await request.post("/sale/D01M01B1/update", JSON.stringify(param));
    if (res.data.code === 200) {
      ElMessage.success("保存成功");
      closeForm();
      emit("changeIdx", res.data.data.id);
      // emit("bindData");
      formdata.value = res.data.data;
    } else {
      ElMessage.warning(res.data.msg || "保存失败");
    }
  } catch (err) {
    console.error(err);
    ElMessage.warning("保存失败");
  }
};
async function deleteForm() {
  try {
    await ElMessageBox.confirm("此操作将永久删除该记录, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    if (props.idx === "0") return;
    const res = await request.get(`/sale/D01M01B3/delete?key=${props.idx}`);
    if (res.data.code === 200) {
      ElMessage.success(res.data.msg || "删除成功");
      emit("closeForm");
    } else {
      ElMessage.warning(res.data.msg || "删除失败");
    }
  } catch (error) {
    // catch 可能来自 confirm 被取消 或 request 失败
    if (error !== "cancel") {
      ElMessage.warning("保存失败");
    }
  } finally {
    submitting.value = 0;
  }
}
const methodsMap: Record<string, () => void> = {
  deleteForm,
};
function clickMethods(val: { meth: string; param: any }) {
  const method = methodsMap[val.meth];
  if (method) {
    method();
  } else {
    console.error(`Method "${val.meth}" not found.`);
  }
}

const closeForm = () => emit("closeForm");

onMounted(() => bindData());

watch(
  () => props.idx,
  () => bindData()
);
</script>

<template>
  <div style="width: 100%; box-sizing: border-box" class="component-container">
    <div style="top: 0px; width: 100%; z-index: 10" class="form_button flex a-c j-end p-r">
      <div class="button-container p-a" style="position: fixed; right: 30px; z-index: 1000">
        <EditGroupBtns
          ref="editGroupBtns"
          :showcontent="['save', 'print', 'operate']"
          :processBar="processBar"
          :operateBar="operateBar"
          :formdata="formdata"
          :formstate="formstate"
          :submitting="submitting"
          @submitForm="submitForm"
          @closeForm="closeForm"
          @clickMethods="clickMethods"
        />
      </div>
    </div>
    <!-- form表单部分-->
    <div style="padding: 20px 20px 20px 20px">
      <!-- 表单容器 -->
      <div
        class="form-border form-container shandow form_info flex f-d-c"
        style="width: 100%"
        :style="{ height: formcontainHeight }"
      >
        <!-- ================头部======================= -->
        <FormTemp
          ref="formtemp"
          style="width: 100%; display: flex; flex-direction: column; height: 100%"
          :formdata="formdata"
          :formtemplate="formtemplate"
          @clickMethods="clickMethods"
        >
          <template #Header>
            <div class="form form-head p-r">
              <D01M01B1EditHeader
                ref="formHeader"
                :title="title"
                :formdata="formdata"
                @getNextCode="getNextCode"
              />
            </div>
          </template>
        </FormTemp>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.component-container {
  background: #e2e2e2;
  height: calc(100vh - 84px);
  overflow: hidden;
  .form-container {
    background: #fff;
  }
  .form {
    margin-right: 20px;
    margin-left: 20px;
    width: calc(100% - 40px);
  }
  .form-head {
    margin-top: calc(20px);
    min-height: 150px;
  }
}
.shandow {
  box-shadow: 0px 0px 10px #e2e2e2;
}
//顶部工具栏
.form_button {
  position: relative;
  .button-container {
    background: #e2e2e2;
    padding: 10px 20px;
  }
}
</style>
