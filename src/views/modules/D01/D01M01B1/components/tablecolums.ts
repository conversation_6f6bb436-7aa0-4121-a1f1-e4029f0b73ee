// tableList.ts

export interface TableColumnItem {
  itemcode: string;
  itemname: string;
  minwidth?: string;
  defwidth?: string;
  displaymark?: number;
  fixed?: number;
  sortable?: number;
  overflow?: number;
  aligntype?: "left" | "center" | "right";
  datasheet?: string;
}

export interface TableList {
  formcode: string;
  item: TableColumnItem[];
}

export const tableList: TableList = {
  formcode: "D01M01B1List",
  item: [
    {
      itemcode: "groupuid",
      itemname: "编码",
      minwidth: "100",
      defwidth: "",
      displaymark: 1,
      fixed: 0,
      sortable: 1,
      overflow: 1,
      aligntype: "center",
      datasheet: "App_Workgroup.groupuid",
    },
    {
      itemcode: "groupname",
      itemname: "名称",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.groupname",
    },
    {
      itemcode: "abbreviate",
      itemname: "简写",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.abbreviate",
    },
    {
      itemcode: "groupclass",
      itemname: "客户类型",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.groupclass",
    },
    {
      itemcode: "seller",
      itemname: "业务员",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.seller",
    },
    {
      itemcode: "groupadd",
      itemname: "地址",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.groupadd",
    },
    {
      itemcode: "linkman",
      itemname: "联系人",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.linkman",
    },
    {
      itemcode: "telephone",
      itemname: "联系电话",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.telephone",
    },
    {
      itemcode: "groupfax",
      itemname: "传真",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.groupfax",
    },
    {
      itemcode: "remark",
      itemname: "备注",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.remark",
    },
    {
      itemcode: "lister",
      itemname: "制表",
      minwidth: "80",
      displaymark: 1,
      overflow: 1,
      datasheet: "App_Workgroup.lister",
    },

    {
      itemcode: "createdate",
      itemname: "创建时间",
      minwidth: "120",
      displaymark: 1,
      overflow: 1,
      sortable: 1,
      datasheet: "App_Workgroup.createdate",
    },
    {
      itemcode: "operate",
      itemname: "操作",
      minwidth: "100",
      displaymark: 1,
      overflow: 1,
    },
  ],
};
