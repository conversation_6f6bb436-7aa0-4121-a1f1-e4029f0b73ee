<script setup lang="ts">
import { ref } from "vue";

interface FormData {
  wggroupname?: string;
  abbreviate?: string;
  id?: number;
  groupuid?: string;
  groupname?: string;
  seller?: string;
  groupclass?: string;
  rownum?: number;
  enabledmark?: number;
  remark?: string;
  linkman?: string;
  telephone?: string;
  mobile?: string;
  groupzip?: string;
  groupfax?: string;
  city?: string;
  groupadd?: string;
}

const props = defineProps<{
  formdata: FormData;
  title?: string;
}>();

const groupclassRef = ref();
const dictionaryRef = ref();

const formRules = {
  groupname: [{ required: true, trigger: "blur", message: "名称为必填项" }],
  groupuid: [{ required: true, trigger: "blur", message: "编码为必填项" }],
};
</script>
<template>
  <el-form
    ref="formdata"
    :model="formdata"
    :label-width="'100px'"
    class="custInfo"
    :rules="formRules"
  >
    <p class="formTitle">{{ props.title }}</p>
    <el-row>
      <el-col :span="5">
        <div>
          <el-form-item label="编码" prop="groupuid">
            <el-input v-model.trim="formdata.groupuid" placeholder="请输入客户编码" size="small">
              <template v-if="!formdata.id" #append>
                <el-button
                  class="getNextCode"
                  icon="EditPen"
                  @click="formdata.groupuid ? '' : $emit('getNextCode')"
                />
              </template>
            </el-input>
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="5">
        <div>
          <el-form-item label="客户" prop="groupname">
            <el-input
              v-model.trim="formdata.groupname"
              placeholder="请输入客户"
              clearable
              size="small"
            />
          </el-form-item>
        </div>
      </el-col>
      <el-col :span="5">
        <el-form-item label="简写">
          <el-input v-model="formdata.abbreviate" placeholder="请输入简写" clearable size="small" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="5">
        <el-form-item label="客户分组">
          <el-input
            v-model="formdata.wggroupname"
            placeholder="请选择客户分组"
            clearable
            size="small"
          />
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label="客户类型">
          <el-popover
            ref="dictionaryRef"
            placement="bottom"
            trigger="click"
            class="salemanStyle"
            @show="groupclassRef.bindData()"
          >
            <template #reference>
              <el-input
                v-model="formdata.groupclass"
                placeholder="请选择客户类型"
                clearable
                size="small"
              />
            </template>
          </el-popover>
        </el-form-item>
      </el-col>
      <el-col :span="4">
        <el-form-item label="状态">
          <el-checkbox
            v-model="formdata.enabledmark"
            label="有效"
            :true-value="1"
            :false-value="0"
            size="small"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <!-- <el-col :span="4">
        <el-form-item label="排序">
          <el-input-number
            v-model="formdata.rownum"
            controls-position="right"
            :min="0"
            size="small"
            style="width: 100%"
          />
        </el-form-item>
      </el-col> -->
      <el-col :span="5">
        <el-form-item label="联系人">
          <el-input v-model="formdata.linkman" placeholder="请输入联系人" clearable size="small" />
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label="电话">
          <el-input v-model="formdata.telephone" placeholder="请输入电话" clearable size="small" />
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label="手机">
          <el-input v-model="formdata.mobile" placeholder="请输入手机" clearable size="small" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="5">
        <el-form-item label="邮编">
          <el-input v-model="formdata.groupzip" placeholder="请输入邮编" clearable size="small" />
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label="传真">
          <el-input v-model="formdata.groupfax" placeholder="请输入传真" clearable size="small" />
        </el-form-item>
      </el-col>
      <el-col :span="5">
        <el-form-item label="业务员">
          <el-input v-model="formdata.seller" placeholder="请输入业务员" clearable size="small" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-form-item label="城市">
        <el-input v-model="formdata.city" placeholder="城市" clearable size="small" />
      </el-form-item>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="地址" label-position="right" label-width="100px">
          <el-input
            v-model="formdata.groupadd"
            type="textarea"
            :rows="4"
            placeholder="请输入地址"
            clearable
            size="small"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped>
.getNextCode {
  font-size: 15px;
  color: #409eff;
  cursor: pointer;
  border-left: 1px solid #dcdfe6;
  padding: 7px 10px;
}
</style>
