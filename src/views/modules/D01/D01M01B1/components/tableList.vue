<script setup lang="ts">
import request from "@/utils/request.js";
import { tableList } from "./tablecolums";
import { ElButton, ElMessage } from "element-plus";
// import { getColumn as fetchColumn } from "@/utils/getColumn";
import { formatDate } from "@/utils/formatTime";

// 类型定义
interface TableItem {
  itemcode: string;
  itemname: string;
  minwidth: number | string;
  displaymark: boolean;
  fixed?: number;
  overflow?: boolean;
  aligntype?: string;
  sortable?: boolean;
}

interface QueryParams {
  PageNum: number;
  PageSize: number;
  OrderType: number;
  SearchType: number;
  scenedata?: any;
  SearchPojo?: {
    groupuid: string;
    groupname: string;
    wggroupid: string;
    abbreviate: string;
  };
  OrderBy?: string;
}

const lst = ref<any[]>([]);
const total = ref(0);
const selectList = ref<any[]>([]);
const customData = ref<any[]>([]);
const columnHidden = ref<string[]>([]);
const keynum = ref<number>(0);
const rowScroll = ref(0);
const tableForm = ref(tableList);

// 表格高度计算
const tableMaxHeight = computed(() => {
  const height = window.innerHeight - 160 - 40;
  return (height < 600 ? 600 : height) + "px";
});

// 表格最小宽度计算
const tableMinWidth = computed(() => {
  let width: number | string = "calc(100vw - 64px)";
  if (tableForm.value?.item?.length) {
    width = 0;
    for (const item of tableForm.value.item) {
      if (item.displaymark) {
        width += Number(item.minwidth) || 0;
      }
    }
  }
  return width;
});

// 分页参数
const queryParams = reactive<QueryParams>({
  PageNum: 1,
  PageSize: 20,
  OrderType: 1,
  SearchType: 1,
});

// 虚拟滚动配置
const virtualScrollOption = reactive({
  enable: true,
  scrolling: ({ startRowIndex }: { startRowIndex: number }) => {
    rowScroll.value = startRowIndex;
  },
});

// 复选框配置
const checkboxOption = reactive({
  selectedRowKeys: [] as any[],
  selectedRowChange: ({ selectedRowKeys }: { selectedRowKeys: any[] }) => {
    // 选中行的id列表
    selectList.value = selectedRowKeys.map((id) => ({ id }));
    checkboxOption.selectedRowKeys = selectedRowKeys;
  },
  selectedAllChange: ({
    isSelected,
    selectedRowKeys,
  }: {
    isSelected: boolean;
    selectedRowKeys: any[];
  }) => {
    checkboxOption.selectedRowKeys = isSelected ? selectedRowKeys : [];
    selectList.value = isSelected ? selectedRowKeys.map((id) => ({ id })) : [];
  },
});

// 排序配置
const sortOption = reactive({
  sortChange: (params: any) => {
    changeSort(params);
  },
});

const changeSort = (row: Record<string, string>) => {
  for (const key in row) {
    if (row[key]) {
      queryParams.OrderType = row[key] === "desc" ? 1 : 0;
      bindData();
      break;
    }
  }
};

// 数据绑定方法
const bindData = async () => {
  try {
    const res = await request.post("/sale/D01M01B1/getPageList", queryParams);
    if (res.data?.code === 200) {
      // 强制添加唯一标识（即使数据本身有id）
      lst.value = (res.data.data?.list || []).map((item: any, index: number) => ({
        ...item,
        _uniqueId: `${item.id || index}_${Date.now()}`, // 复合唯一键
      }));
      total.value = res.data.data?.total || 0;
    }
  } catch {
    ElMessage.error("请求错误");
  }
};

// 获取列配置
const getColumn = async () => {
  initTable(tableForm.value);
  // try {
  //   const data = await fetchColumn(tableForm.value.formcode, tableList);
  //   if (data?.colList) {
  //     tableForm.value = { ...data.colList };
  //     // $emit("sendTableForm", tableForm.value);
  //   }
  // } catch (error) {
  //   console.error("获取列配置失败:", error);
  //   ElMessage.error("获取列配置失败");
  // }
};
const deleteRows = async () => {
  if (selectList.value.length === 0) {
    ElMessage.warning("请选择要删除的行");
    return;
  } else {
    ElMessageBox.confirm("此操作将永久删除该记录, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        try {
          for (const item of selectList.value) {
            await request.get(`/sale/D01M01B3/delete?key=${item.id}`);
          }
          selectList.value = [];
          checkboxOption.selectedRowKeys = [];
          ElMessage.success("删除成功");
          bindData();
        } catch (error) {
          console.error(error);
          ElMessage.error("删除失败");
        }
      })
      .catch(() => {
        ElMessage.info("已取消");
      });
  }
};
const search = (val: string) => {
  if (val !== "") {
    queryParams.SearchPojo = {
      groupuid: val,
      groupname: val,
      wggroupid: val,
      abbreviate: val,
    };
  } else {
    delete queryParams.SearchPojo; // 删除 SearchPojo 属性
  }
  queryParams.SearchType = 1;
  queryParams.PageNum = 1;
  bindData(); // 调用 bindData 方法
};
// 初始化表格
const initTable = (data: any) => {
  if (!data?.item) return;

  const columns = data.item.map((item: TableItem) => {
    // 缓存常用值
    const itemCode = item.itemcode;
    const isSpecialColumn = [
      "enabledmark",
      "createdate",
      "modifydate",
      "groupuid",
      "operate",
    ].includes(itemCode);
    // 处理特殊列
    return {
      field: itemCode,
      key: itemCode,
      title: item.itemname,
      width: Number(item.minwidth) || 10,
      displaymark: item.displaymark,
      fixed: item.fixed ? (item.fixed === 1 ? "left" : "right") : false,
      ellipsis: item.overflow ? { showTitle: true } : false,
      align: item.aligntype || "center",
      sortBy: item.sortable ? "" : false,
      renderBodyCell: ({ row }: { row: any }) => {
        // 普通列直接返回值
        if (!isSpecialColumn) return row[itemCode];
        // 特殊列返回渲染函数
        if (itemCode === "enabledmark") {
          return row[itemCode] == 1 ? "正常" : "停用";
        } else if (
          itemCode === "enabledmark" ||
          item.itemcode == "modifydate" ||
          item.itemcode == "createdate"
        ) {
          return formatDate(new Date(row[item.itemcode]), "YYYY-mm-dd");
        } else if (itemCode === "groupuid") {
          return h(
            ElButton,
            {
              type: "default",
              size: "small",
              class: "custom-text-style",
              onClick: () => {
                emit("showForm", row.id);
              },
            },
            () => (row[item.itemcode] ? row[item.itemcode] : "单据编码")
          );
        } else if (itemCode === "operate") {
          return h(
            ElButton,
            {
              type: "default",
              size: "small",
              class: "custom-text-style",
              onClick: () => {
                console.log("点击了编辑按钮", row);
              },
            },
            () => (row[item.itemcode] ? row[item.itemcode] : "操作")
          );
        }
      },
    };
  });

  // 更新隐藏列
  columnHidden.value = data.item
    .filter((item: TableItem) => !item.displaymark)
    .map((item: TableItem) => item.itemcode);

  // 构建最终列配置
  customData.value = [
    {
      field: "",
      key: "checkbox",
      type: "checkbox",
      title: "",
      width: 50,
      align: "center",
      fixed: "left",
      resizable: true,
    },
    {
      field: "index",
      key: "index",
      title: "ID",
      width: 50,
      align: "center",
      fixed: "left",
      renderBodyCell: ({ rowIndex }: { rowIndex: number }) => rowIndex + rowScroll.value + 1,
      resizable: true,
    },
    ...columns,
  ];

  // 强制更新表格
  keynum.value += 1;
};
function getList(data: { page: number; limit: number }) {
  queryParams.PageNum = data.page;
  queryParams.PageSize = data.limit;
  bindData();
}
defineExpose({
  bindData,
  getColumn,
  deleteRows,
  search,
});
const emit = defineEmits(["showForm"]);
</script>

<template>
  <div class="pageWhite">
    <ve-table
      :key="keynum"
      row-key-field-name="id"
      :max-height="tableMaxHeight"
      :scroll-width="tableMinWidth"
      :style="{ 'word-break': 'break-all' }"
      :is-horizontal-resize="true"
      :fixed-header="true"
      :columns="customData"
      :table-data="lst"
      :border-x="true"
      :border-y="true"
      :border-around="true"
      :column-hidden-option="{ defaultHiddenColumnKeys: columnHidden }"
      :column-width-resize-option="{ enable: true, minWidth: 50 }"
      :virtual-scroll-option="virtualScrollOption"
      :checkbox-option="checkboxOption"
      :fixed-footer="true"
      :sort-option="sortOption"
      :cell-class-name="() => 'custom-cell'"
    />
    <pagination
      v-if="total > 0"
      :total="total"
      :page="queryParams.PageNum"
      :limit="queryParams.PageSize"
      @pagination="getList"
    />
  </div>
</template>
<style scoped>
:deep(.custom-text-style) {
  background: none;
  border: none;
  color: #409eff;
  padding: 4px 15px;
  text-decoration: none;
  outline: none;
}
</style>
