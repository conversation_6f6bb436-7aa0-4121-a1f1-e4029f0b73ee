import request from "@/utils/request";
const CRUD = {
    // get user info 保存
    add(formdata) {
        return new Promise((resolve, reject) => {
            
            var params = JSON.stringify(formdata)
            request
                .post("/system/SYSM08B1/create", params) 
                .then((response) => {
                    console.log(params,response)
                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });
        })
    },
    // 更新添加
      update(formdata) {
        return new Promise((resolve, reject) => {
            
            var params = JSON.stringify(formdata);
            
            request
                .post("/system/SYSM08B1/update", params) 
                .then((response) => {
                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });
        })
    },
    delete(idx) {
        return new Promise((resolve, reject) => {
           // 删除执行
           request
           .get(`/system/SYSM08B1/delete?key=${idx}`) 
           .then((response) => {
               
               if (response.data.code == 200) {
                   resolve(response.data);
               } else {
                   reject(response.data.msg)
               }
           })
           .catch((error) => {
               reject(error)
           });
        })
    },
}

export default CRUD