<template>
  <div v-loading="listLoading" element-loading-text="Loading...">
    <div v-if="FormVisible" ref="formedit" class="formedit">
      <formedit ref="formedit" :idx="idx" @compForm="compForm" @closeForm="closeForm" />
    </div>
    <div v-else class="page-container">
      <listheader
        @btnSearch="search"
        @bindData="bindData"
        @btndelete="btndelete"
        @advancedSearch="advancedSearch"
      />
      <div>
        <el-table
          :data="lst"
          element-loading-text="Loading"
          border
          fit
          highlight-current-row
          style="overflow: auto"
          :header-cell-style="{
            background: '#F3F4F7',
            color: '#555',
          }"
          :cell-style="{ padding: '6px 0px 6px 0px' }"
          :height="tableMaxHeight"
          @selection-change="handleSelectionChange"
        >
          <!-- <el-table-column type="selection" align="center" width="55"></el-table-column> -->
          <el-table-column align="center" label="ID" width="50px">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="用户账号" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.username }}</span>
            </template>
          </el-table-column>
          <el-table-column label="用户名称" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.realname }}</span>
            </template>
          </el-table-column>
          <el-table-column label="租户" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.tenantname }}</span>
            </template>
          </el-table-column>
          <el-table-column label="IP地址" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.ipaddr }}</span>
            </template>
          </el-table-column>
          <el-table-column label="地址" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.loginlocation }}</span>
            </template>
          </el-table-column>
          <el-table-column label="浏览器" align="center" min-width="80px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.browsername }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作系统" align="center" min-width="80px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.hostsystem }}</span>
            </template>
          </el-table-column>
          <el-table-column label="登录/登出" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.direction }}</span>
            </template>
          </el-table-column>
          <el-table-column label="登录状态" align="center" min-width="50px" show-overflow-tooltip>
            <template #default="scope">
              <el-tag v-if="scope.row.loginstatus == 0">成功</el-tag>
              <el-tag v-else type="danger">失败</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作信息" align="center" min-width="150px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.loginmsg }}</span>
            </template>
          </el-table-column>
          <el-table-column label="登录日期" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatDate(scope.row.logintime) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page="queryParams.PageNum"
          :limit="queryParams.PageSize"
          @pagination="GetList"
          @update:page="(val) => (queryParams.PageNum = val)"
          @update:limit="(val) => (queryParams.PageSize = val)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import listheader from "./components/listheader.vue";
import Pagination from "@/components/Pagination/index.vue";
import request from "@/utils/request";
import formedit from "./components/formedit.vue";

// 响应式数据
const lst = ref([]); // 列表数据
const FormVisible = ref(false);
const idx = ref(0);
const searchstr = ref(""); // sql查询文本
const total = ref(0);
const selectedList = ref([]);
const listLoading = ref(false);
const queryParams = reactive({
  PageNum: 1,
  PageSize: 20,
  OrderType: 1,
  SearchType: 1,
  OrderBy: "logintime",
  // SearchPojo:{'tenantid':this.$store.state.user.userinfo.Tenantid}
});

// 计算属性
const tableMaxHeight = computed(() => {
  return window.innerHeight - 160 + "px";
});

// 日期格式化函数
const formatDate = (dataStr) => {
  if (!dataStr) return "";
  const dt = new Date(dataStr);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1).toString().padStart(2, "0");
  const d = dt.getDate().toString().padStart(2, "0");
  const hh = dt.getHours().toString().padStart(2, "0");
  const mm = dt.getMinutes().toString().padStart(2, "0");
  const ss = dt.getSeconds().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
};
// 方法
// 加载列表
const bindData = () => {
  listLoading.value = true;
  request
    .post("/system/SYSM08B1/getPageList", JSON.stringify(queryParams))
    .then((response) => {
      if (response.data.code == 200) {
        lst.value = response.data.data.list;
        total.value = response.data.data.total;
      }
      listLoading.value = false;
    })
    .catch(() => {
      listLoading.value = false;
    });
};

// 查询
const GetList = (data) => {
  queryParams.PageNum = data.page;
  queryParams.PageSize = data.limit;
  bindData();
};

// 查询
const search = (res) => {
  if (res != "") {
    queryParams.SearchPojo = { username: res, realname: res, direction: res };
  } else {
    delete queryParams.SearchPojo;
  }
  queryParams.SearchType = 1;
  queryParams.PageNum = 1;
  bindData();
};

// 高级搜索
const advancedSearch = (val) => {
  queryParams.SearchPojo = val;
  queryParams.SearchType = 0; // 是否开启高级搜索
  queryParams.PageNum = 1;
  bindData();
};

const btndelete = () => {
  console.log("批量删除");
  if (selectedList.value.length == 0) {
    ElMessage.warning("请选择要删除的日志");
    return;
  }
  request
    .get(`/system/SYSM08B1/delete`, JSON.stringify(selectedList.value))
    .then((response) => {
      if (response.data.code == 200) {
        ElMessage.success(response.data.msg);
        bindData();
      } else {
        ElMessage.warning(response.data.msg);
      }
    })
    .catch(() => {
      ElMessage.warning("删除失败");
    });
};

// 获取选中的数组
const handleSelectionChange = (val) => {
  console.log(val);
  selectedList.value = val;
};

// 表单相关方法
const compForm = () => {
  FormVisible.value = false;
  bindData();
};

const closeForm = () => {
  FormVisible.value = false;
};

// 初始化
onMounted(() => {
  searchstr.value = "";
  bindData();
});
</script>

<style scoped>
.page-container {
  padding: 0;
  height: calc(100vh - 105px - 37px);
}
.cab :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}
.cab :deep(.is-horizontal) {
  display: none;
}
.table-container {
  border: 1px solid #ddd;
  border-top: 0;
  background: #f3f4f7;
}
.lst-card {
  display: flex;
  flex-wrap: wrap;
}
.lst-card-item {
  width: 18%;
  margin: 6px 1%;
  padding: 15px;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #d1d1d1;
  box-shadow:
    4px 4px 2px #cccccc,
    1px 1px 0px 1px #cccccc;
  display: flex;
  cursor: pointer;
}
.lst-card-item-right {
  margin-left: 10px;
}
.lst-card-item-right h3 {
  margin: 0;
  margin-bottom: 6px;
  letter-spacing: 4px;
}
.lst-card-item-right span {
  font-size: 12px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
:deep(.pagination) {
  padding: 0;
  margin-left: 10px;
}
</style>
