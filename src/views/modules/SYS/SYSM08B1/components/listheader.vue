<template>
  <div>
    <div :class="iShow ? 'filter-container flex j-s ishowDetail' : 'filter-container flex j-s'">
      <div>
        <el-input
          v-model="strfilter"
          placeholder="请输入查询"
          :prefix-icon="Search"
          style="width: 260px; height: 100%"
          class="filter-item"
          size="default"
          @keyup.enter.native="btnSearch"
        >
          <template #append>
            <el-button class="filter-item" size="default" @click="btnSearch">搜索</el-button>
          </template>
        </el-input>
        <!-- <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="danger"
          icon="el-icon-delete"
          size="mini"
          plain
          @click="btndelete"
        >
          删除
        </el-button> -->
      </div>

      <div class="iShowBtn">
        <el-button size="default" :icon="Setting"></el-button>
        <el-button
          size="default"
          :icon="Refresh"
          icon="el-icon-refresh-right"
          title="刷新"
          @click="bindData"
        ></el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px"
          :icon="iShow ? ArrowUp : ArrowDown"
          size="default"
          title="高级搜索"
          @click="iShow = !iShow"
        />
      </div>
    </div>
    <el-collapse-transition>
      <div v-show="iShow" class="searchDetail">
        <el-form :model="formdata" label-width="100px" auto-complete="on">
          <el-row>
            <el-col :span="5">
              <el-form-item label="用户账号">
                <el-input
                  v-model="formdata.username"
                  clearable
                  placeholder="请输入用户账号"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="用户名称">
                <el-input
                  v-model="formdata.realname"
                  clearable
                  placeholder="请输入用户名称"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="IP地址">
                <el-input
                  v-model="formdata.ipaddr"
                  clearable
                  placeholder="请输入IP地址"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="浏览器">
                <el-input
                  v-model="formdata.browsername"
                  clearable
                  placeholder="请输入浏览器"
                  size="default"
                />
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-button
                class="filter-item"
                style="margin-left: 10px"
                type="primary"
                size="default"
                @click="advancedSearch()"
              >
                搜索
              </el-button>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="5">
              <el-form-item label="登录/登出">
                <el-input
                  v-model="formdata.direction"
                  placeholder="请输入登录/登出"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="操作系统">
                <el-input
                  v-model="formdata.hostsystem"
                  placeholder="请输入操作系统"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="操作信息">
                <el-input v-model="formdata.loginmsg" placeholder="请输入操作信息" size="default" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup>
import { Search, Refresh, ArrowDown, ArrowUp, Setting } from "@element-plus/icons-vue";
import { ref, reactive } from "vue";

// 定义 emits
const emit = defineEmits(["advancedSearch", "btndelete", "btnSearch", "bindData"]);

// 响应式数据
const strfilter = ref("");
const iShow = ref(false);
const formdata = reactive({});

// 方法
const advancedSearch = () => {
  iShow.value = false;
  emit("advancedSearch", formdata);
};

const btnSearch = () => {
  emit("btnSearch", strfilter.value);
};

const bindData = () => {
  emit("bindData");
};
</script>
<style lang="scss" scoped>
.filter-container {
  margin: 0 10px;
  padding: 4px 10px;
  box-sizing: border-box;
  width: 98%;
  position: relative;
  align-items: flex-end;
}

.ishowDetail {
  border: 1px solid #ddd;
  border-bottom: 0;
}
.searchDetail {
  position: absolute;
  top: 38px;
  z-index: 99;
  background-color: #fff;
  width: 98%;
  border: 1px solid #ddd;
  border-top: 0px #ddd dotted;
  left: 10px;
  padding: 10px 10px;
  box-sizing: border-box;
  border-radius: 0 0 4px 4px;
}
.flex {
  display: flex;
  align-items: center;
}
.a-c {
  align-items: center;
}
.j-s {
  justify-content: space-between;
}
.iShowBtn {
  margin-right: 10px;
  cursor: pointer;
  i:hover,
  i:active {
    color: #409eff;
  }
}
</style>
