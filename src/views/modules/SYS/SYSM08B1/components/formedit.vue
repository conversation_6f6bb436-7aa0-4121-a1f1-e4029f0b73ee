<template>
  <div style="width: 100%; box-sizing: border-box" class="component-container">
    <div style="top: 0px; width: 100%; z-index: 10" class="form_button flex a-c j-end p-r">
      <div class="button-container p-a" style="top: 0px; right: 30px">
        <el-button type="primary" size="default" @click.native="submitForm('formdata')">
          保 存
        </el-button>
        <el-button size="default" @click.native="closeForm">关 闭</el-button>
      </div>
    </div>
    <!-- =======================form表单部分=================================== -->
    <div style="padding: 20px 20px 20px 20px">
      <!-- 表单容器 -->
      <div
        class="form-border form-container shandow form_info flex f-d-c"
        style="width: 100%"
        :style="{ height: formcontainHeight }"
      >
        <!-- ================头部======================= -->
        <div ref="form_main_info" class="form form-head p-r">
          <el-form
            :model="formdata"
            :label-width="formLabelWidth"
            class="custInfo"
            auto-complete="on"
            :rules="formRules"
          >
            <el-row>
              <el-col :span="6">
                <el-form-item label="上级菜单" prop="navPid">
                  <el-input
                    v-model="formdata.navPid"
                    placeholder="请选择上级菜单"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <div />
              </el-col>
              <el-col :span="4">
                <div />
              </el-col>
              <el-col :span="4">
                <div />
              </el-col>
              <!-- 替代被上方单据编号的位置 默认6保留 -->
              <el-col :span="6">
                <div />
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="菜单类型">
                  <el-radio v-model="formdata.navType" label="0">项目</el-radio>
                  <el-radio v-model="formdata.navType" label="1">页面</el-radio>
                  <el-radio v-model="formdata.navType" label="2">分组</el-radio>
                  <el-radio v-model="formdata.navType" label="3">按键</el-radio>
                </el-form-item>
              </el-col>
              <el-col :span="8" />
              <el-col :span="8" />
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="菜单编码">
                  <el-input
                    v-model="formdata.navCode"
                    placeholder="请输入菜单编码"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="菜单名称">
                  <el-input
                    v-model="formdata.navName"
                    placeholder="请输入菜单名称"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="显示排序">
                  <el-input-number
                    v-model="formdata.rowNum"
                    :controls="true"
                    type="number"
                    :min="0"
                    controls-position="right"
                    class="inputNumberContent"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="菜单图标">
                  <el-input
                    v-model="formdata.imageIndex"
                    placeholder="请选择菜单图标"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="图标样式">
                  <el-input
                    v-model="formdata.imageStyle"
                    placeholder="请输入图标样式"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6" />
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="路由地址">
                  <el-input
                    v-model="formdata.mvcUrl"
                    placeholder="请输入路由地址"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                    :auto-complete="true"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="web地址">
                  <el-input
                    v-model="formdata.navigateUrl"
                    placeholder="请输入web地址"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                    :auto-complete="true"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8" />
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="权限字符">
                  <el-input
                    v-model="formdata.permissionCode"
                    placeholder="请输入权限字符"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                    :auto-complete="true"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="分组编码">
                  <el-input
                    v-model="formdata.navGroup"
                    placeholder="请输入分组编码"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                    :auto-complete="true"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="角色编码">
                  <el-input
                    v-model="formdata.roleCode"
                    placeholder="请输入角色编码"
                    clearable
                    size="default"
                    style="width: 100%; min-width: 140px"
                    :auto-complete="true"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="4">
                <el-form-item label="菜单状态">
                  <el-radio v-model="formdata.enabledMark" :label="1">正常</el-radio>
                  <el-radio v-model="formdata.enabledMark" :label="0">停用</el-radio>
                </el-form-item>
              </el-col>
              <el-col :span="4" />
              <el-col :span="4" />
            </el-row>
          </el-form>
          <el-divider />
        </div>
        <!-- <div class="f-1"></div> -->
        <!-- ==================================表尾================================= -->

        <el-form :label-width="formLabelWidth" class="footFormContent">
          <el-row style="margin-top: 15px; margin-right: 20px">
            <el-col :span="24">
              <el-form-item label="摘  要" label-position="right" label-width="100px">
                <el-input
                  v-model="formdata.remark"
                  placeholder="请输入摘要"
                  clearable
                  size="default"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="4">
              <el-form-item label="制表">
                <span v-show="formdata.Lister" class="el-form-item__label">
                  {{ formdata.Lister }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="创建日期">
                <span v-show="formdata.CreateDate" class="el-form-item__label">
                  {{ formatDate(formdata.CreateDate) }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="修改日期">
                <span v-show="formdata.CreateDate" class="el-form-item__label">
                  {{ formatDate(formdata.ModifyDate) }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <!-- ===========================组件============================ -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import request from "@/utils/request";
import CRUD from "../CRUD.JS";

// 定义 props
const props = defineProps({
  idx: {
    type: [String, Number],
    default: 0,
  },
  title: {
    type: String,
    default: "",
  },
});

// 定义 emits
const emit = defineEmits(["compForm", "closeForm"]);

// 验证函数
const validatetest = (rule, value, callback) => {
  console.log(value);
  if (value.trim().length == 0) {
    callback(new Error("请选择员工"));
  } else {
    callback();
  }
};

// 响应式数据
const listLoading = ref(false);
const formdata = reactive({
  navid: 1, // Navid
  navGroup: "", // 分组编码
  navCode: "", // 导航编码
  navName: "", // 导航名称
  navType: "0", // 导航类型
  navPid: "", // 父级id
  rowNum: 0, // 排列序号
  imageCss: "", // Css图标
  iconUrl: "", // Web图标
  navigateUrl: "", // Web位置 url
  mvcUrl: "", // MVC位置
  moduleType: "", // 模块类型
  moduleCode: "", // 模块编码
  roleCode: "", // 角色编码
  imageIndex: "", // 图标
  imageStyle: "", // 图标样式
  enabledMark: 1, // 有效标识
  remark: "", // 备注
  permissionCode: "", // 许可编码
  functionCode: "", // 服务编码
  lister: "", // 制表
  createDate: "", // 创建日期
  modifyDate: "", // 修改日期
  deleteMark: 0, // 删除标识
  deleteLister: "", // 删除人
  deleteDate: "", // 删除日期
});

// form验证内容
const formRules = reactive({
  test: [{ required: true, trigger: "blur", validator: validatetest }],
});

const formLabelWidth = ref("100px");

// 日期格式化函数
const formatDate = (dataStr) => {
  if (!dataStr) return "";
  const dt = new Date(dataStr);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1).toString().padStart(2, "0");
  const d = dt.getDate().toString().padStart(2, "0");
  const hh = dt.getHours().toString().padStart(2, "0");
  const mm = dt.getMinutes().toString().padStart(2, "0");
  const ss = dt.getSeconds().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
};
// 计算属性
const formcontainHeight = computed(() => {
  return window.innerHeight - 50 - 33 - 50 + "px";
});

// 监听器
watch(
  () => props.idx,
  (val, oldVal) => {
    console.log("new: %s, old: %s", val, oldVal);
    bindData();
  }
);
// 方法
// 加载列表
const bindData = () => {
  listLoading.value = true;
  console.log("绑定数据");
  request
    .get(`/system/SYSM05B2/getEntity?key=${props.idx}`)
    .then((response) => {
      console.log(response);
      console.log(`get了idx=${props.idx}`);
      if (response.data.Success) {
        console.log("查看编辑");
        console.log(JSON.parse(response.data.Data));
        Object.assign(formdata, response.data);
      }
      listLoading.value = false;
    })
    .catch(() => {
      listLoading.value = false;
    });
};

// el默认表单提交验证
const submitForm = (formdataRef) => {
  formdataRef.validate((valid) => {
    if (valid) {
      console.log("可保存");
      saveForm();
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};
// 向index.vue父组件触发forscomp事件 现在修改为在本组件下执行保存 父组件执行关闭 传elitem的lst的原因是vue不建议直接改子组件得到的prop值
const saveForm = () => {
  if (props.idx == 0) {
    console.log("新建保存", formdata);
    CRUD.add(formdata)
      .then(() => {
        emit("compForm");
      })
      .catch(() => {
        ElMessage({
          showClose: true,
          message: "保存失败",
          type: "warning",
        });
      });
  } else {
    CRUD.update(formdata)
      .then(() => {
        emit("compForm");
      })
      .catch(() => {
        ElMessage({
          showClose: true,
          message: "保存失败",
          type: "warning",
        });
      });
    console.log("修改保存", props.idx);
    console.log(formdata);
  }
};

// 关闭formedit对话框
const closeForm = () => {
  emit("closeForm");
  console.log("关闭窗口");
};

// 初始化
onMounted(() => {
  console.log(props.idx);
  bindData();
});
</script>
<style scoped lang="scss">
//灰色背景
$bg-gray: #e2e2e2;
$bg-component: #f5f5f5;

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}
:deep(.is-horizontal) {
  display: none;
}
:deep(.custInfo .el-row) {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
/**修改错误提示 */
:deep(.el-form-item__error) {
  top: 1px;
  background: #fff;
  height: 25px;
  margin-top: 5px;
  margin-left: 5px;
  width: 120px;
  display: flex;
  align-items: center;
  // display: flex;
  // flex-wrap: wrap ;
}
// ::v-depp .el-input__inner{

// }
/**美化用  外框阴影*/
.shandow {
  box-shadow: 0px 0px 10px $bg-gray;
}
/**位置css */
.p-r {
  position: relative;
}
.p-a {
  position: absolute;
}
.p-f {
  position: fixed;
}
/**flex css  */
.flex {
  display: flex;
  flex-wrap: wrap;
}
.f-1 {
  flex: 1;
}

.f-d-c {
  flex-direction: column;
}

.j-c {
  justify-content: center;
}

.a-c {
  align-items: center;
}

.j-s {
  justify-content: space-between;
}

.j-start {
  justify-content: flex-start;
}
.j-end {
  justify-content: flex-end;
}
//顶部工具栏
.form_button {
  .button-container {
    background: $bg-gray;
    padding: 10px;
  }
}
//表格边框
.form-border {
  border: 2px solid #dbdbdb;
}
.component-container {
  background: $bg-component;
  height: calc(100vh - 84px);
  .form-container {
    background: #fff;
  }
  .form {
    margin-right: 20px;
    margin-left: 20px;
    width: calc(100% - 40px);
  }
  .form-head {
    //工具栏上下padding 加按键高度 -容器的上padding
    margin-top: calc(10px + 32px + 10px - 20px);
    .refNo {
      margin-right: 30px;
      width: 300px;
    }
  }
}
/**更改 表单label的字体格式 */
:deep(.el-form-item__label) {
  font-size: 12px;
  font-weight: 700;
}
</style>
