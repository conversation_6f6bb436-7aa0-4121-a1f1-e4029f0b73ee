<template>
  <div v-loading="listLoading" element-loading-text="Loading...">
    <div v-if="FormVisible" ref="formedit" class="formedit">
      <formedit ref="formedit" :idx="idx" @compForm="compForm" @closeForm="closeForm" />
    </div>
    <div v-else class="page-container">
      <listheader
        @btnSearch="search"
        @bindData="bindData"
        @btndelete="btndelete"
        @advancedSearch="advancedSearch"
      />
      <div>
        <el-table
          :data="lst"
          element-loading-text="Loading"
          border
          fit
          highlight-current-row
          style="overflow: auto"
          :header-cell-style="{
            background: '#F3F4F7',
            color: '#555',
          }"
          :cell-style="{ padding: '6px 0px 6px 0px' }"
          :height="tableMaxHeight"
          @selection-change="handleSelectionChange"
        >
          <!-- <el-table-column type="selection" align="center" width="55"> </el-table-column> -->
          <el-table-column label="模块标题" align="center" width="150px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.opertitle }}</span>
            </template>
          </el-table-column>
          <el-table-column label="业务类型" align="center" min-width="60px">
            <template #default="scope">
              <el-tag v-if="scope.row.businesstype == 0" type="info">其它</el-tag>
              <el-tag v-else-if="scope.row.businesstype == 1">新增</el-tag>
              <el-tag v-else-if="scope.row.businesstype == 2" type="success">修改</el-tag>
              <el-tag v-else type="danger">删除</el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="方法名称" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.method }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="操作人员" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.opername }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="请求URL"
            align="center"
            min-width="100px"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span>{{ scope.row.operurl }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="主机地址" align="center" min-width="100px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.operip }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作地点" align="center" min-width="100px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.operlocation }}</span>
            </template>
          </el-table-column>
          <el-table-column label="请求参数" align="center" min-width="100px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.operparam }}</span>
            </template>
          </el-table-column>
          <el-table-column label="返回参数" align="center" min-width="200px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.jsonresult }}</span>
            </template>
          </el-table-column>

          <el-table-column label="错误消息" align="center" min-width="100px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ scope.row.errormsg }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作状态" align="center" min-width="60px" show-overflow-tooltip>
            <template #default="scope">
              <el-tag v-if="scope.row.status == 0">正常</el-tag>
              <el-tag v-else type="danger">异常</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作日期" align="center" min-width="80px" show-overflow-tooltip>
            <template #default="scope">
              <span>{{ formatDate(scope.row.opertime) }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page="queryParams.PageNum"
          :limit="queryParams.PageSize"
          @pagination="GetList"
          @update:page="(val) => (queryParams.PageNum = val)"
          @update:limit="(val) => (queryParams.PageSize = val)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import listheader from "./components/listheader.vue";
import Pagination from "@/components/Pagination/index.vue";
import request from "@/utils/request";
import formedit from "./components/formedit.vue";

// 响应式数据
const lst = ref([]); // 列表数据
const FormVisible = ref(false);
const idx = ref(0);
const searchstr = ref(""); // sql查询文本
const total = ref(0);
const selectedList = ref([]);
const listLoading = ref(false);
const queryParams = reactive({
  PageNum: 1,
  PageSize: 20,
  OrderType: 1,
  SearchType: 1,
  OrderBy: "opertime",
});

// 计算属性
const tableMaxHeight = computed(() => {
  return window.innerHeight - 160 + "px";
});

// 日期格式化函数
const formatDate = (dataStr) => {
  if (!dataStr) return "";
  const dt = new Date(dataStr);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1).toString().padStart(2, "0");
  const d = dt.getDate().toString().padStart(2, "0");
  const hh = dt.getHours().toString().padStart(2, "0");
  const mm = dt.getMinutes().toString().padStart(2, "0");
  const ss = dt.getSeconds().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
};
// 方法
// 加载列表
const bindData = () => {
  listLoading.value = true;
  request
    .post("/system/SYSM08B2/getPageList", JSON.stringify(queryParams))
    .then((response) => {
      console.log("=====00000000======", response);
      if (response.data.code == 200) {
        lst.value = response.data.data.list;
        total.value = response.data.data.total;
      }
      listLoading.value = false;
    })
    .catch(() => {
      listLoading.value = false;
    });
};

// 查询
const GetList = (data) => {
  queryParams.PageNum = data.page;
  queryParams.PageSize = data.limit;
  bindData();
};

// 查询
const search = (res) => {
  if (res != "") {
    queryParams.SearchPojo = { opertitle: res, method: res, opername: res };
  } else {
    delete queryParams.SearchPojo;
  }
  queryParams.SearchType = 1;
  queryParams.PageNum = 1;
  bindData();
};

// 高级搜索
const advancedSearch = (val) => {
  queryParams.SearchPojo = val;
  queryParams.SearchType = 0; // 是否开启高级搜索
  queryParams.PageNum = 1;
  bindData();
};
const btndelete = () => {
  console.log("批量删除");
  if (selectedList.value.length == 0) {
    ElMessage.warning("请选择要删除的日志");
    return;
  }
  request
    .get(`/system/SYSM08B2/delete`, JSON.stringify(selectedList.value))
    .then((response) => {
      if (response.data.code == 200) {
        ElMessage.success(response.data.msg);
        bindData();
      } else {
        ElMessage.warning(response.data.msg);
      }
    })
    .catch(() => {
      ElMessage.warning("删除失败");
    });
};

// 获取选中的数组
const handleSelectionChange = (val) => {
  console.log(val);
  selectedList.value = val;
};

// 表单相关方法
const compForm = () => {
  FormVisible.value = false;
  bindData();
};

const closeForm = () => {
  FormVisible.value = false;
};

// 初始化
onMounted(() => {
  searchstr.value = "";
  bindData();
});
</script>

<style scoped>
.page-container {
  padding: 0;
  height: calc(100vh - 105px - 37px);
}
.cab :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}
.cab :deep(.is-horizontal) {
  display: none;
}
.table-container {
  border: 1px solid #ddd;
  border-top: 0;
  background: #f3f4f7;
}
.lst-card {
  display: flex;
  flex-wrap: wrap;
}
.lst-card-item {
  width: 18%;
  margin: 6px 1%;
  padding: 15px;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #d1d1d1;
  box-shadow:
    4px 4px 2px #cccccc,
    1px 1px 0px 1px #cccccc;
  display: flex;
  cursor: pointer;
}
.lst-card-item-right {
  margin-left: 10px;
}
.lst-card-item-right h3 {
  margin: 0;
  margin-bottom: 6px;
  letter-spacing: 4px;
}
.lst-card-item-right span {
  font-size: 12px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
:deep(.pagination) {
  padding: 0;
  margin-left: 10px;
}
</style>
