<template>
  <div>
    <div class="filter-container" style="margin-bottom: 5px">
      <el-input
        v-model="strfilter"
        placeholder="请输入查询"
        style="width: 200px"
        class="filter-item"
        size="small"
        @keyup.enter="search(strfilter)"
      />
      <el-button class="filter-item" type="primary" size="small" @click="search(strfilter)">
        查询
      </el-button>
    </div>
    <div>
      <el-table
        ref="selectVal"
        v-loading="listLoading"
        :data="lst"
        element-loading-text="Loading"
        border
        fit
        height="350px"
        highlight-current-row
        style="overflow: auto"
        :header-cell-style="{
          background: '#F3F4F7',
          color: '#555',
          padding: '3px 0px 3px 0px',
        }"
        :cell-style="{ padding: '4px 0px 4px 0px' }"
        :row-class-name="rowIndex"
        @row-click="rowClick"
      >
        <!-- ---复选列--- -->
        <el-table-column v-if="multi == 1" type="selection" width="40" />
        <!-- ---单选列--- -->
        <el-table-column v-else label="" width="40" show-overflow-tooltip>
          <template #default="scope">
            <el-radio v-model="radio" :label="scope.$index" @change="getCurrentRow(scope.row)">
              {{ " " }}
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="用户账号" align="center" min-width="100px" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.username }}</span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center" min-width="80px" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.realname }}</span>
          </template>
        </el-table-column>
        <el-table-column label="手机" align="center" prop="mobile" min-width="60">
          <template #default="scope">
            <span>{{ scope.row.mobile ? scope.row.mobile : "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="邮箱" align="center" min-width="60">
          <template #default="scope">
            <span>{{ scope.row.email ? scope.row.email : "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="身份" align="center" min-width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.isadmin == 0" size="medium">普通用户</el-tag>
            <el-tag v-else type="warning" size="medium">管理员</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createdate" min-width="80">
          <template #default="scope">
            <span>{{ dateFormat(scope.row.createdate) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.PageNum"
      v-model:limit="queryParams.PageSize"
      :total="total"
      @pagination="GetList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import request from "@/utils/request";
import Pagination from "@/components/Pagination/index.vue";

// Emits
const emit = defineEmits(["singleSel"]);

const listLoading = ref(true);
const lst = ref([]); //  列表数据
const searchstr = ref(" "); // sql查询文本
const strfilter = ref(""); // 搜索框内输入的值
const total = ref(0);
const radio = ref(""); // 单选用参数默认为0即可
const selrows = ref(""); // 选择的内容 单选变量
const queryParams = reactive({
  PageNum: 1,
  PageSize: 10,
  OrderType: 1,
  SearchType: 1,
  //  SearchPojo:{'tenantid':this.$store.state.user.userinfo.Tenantid}
});

// 日期格式化方法（替代过滤器）
const dateFormat = (dataStr) => {
  if (!dataStr) {
    return;
  }
  var dt = new Date(dataStr);
  var y = dt.getFullYear();
  var m = (dt.getMonth() + 1).toString().padStart(2, "0");
  var d = dt.getDate().toString().padStart(2, "0");
  // var hh = dt.getHours().toString().padStart(2, "0");
  // var mm = dt.getMinutes().toString().padStart(2, "0");
  // var ss = dt.getSeconds().toString().padStart(2, "0"); ${hh}:${mm}:${ss}
  return `${y}-${m}-${d}`;
};

// 生命周期
onMounted(() => {
  searchstr.value = "";
  bindData();
});
// 方法
//  单选列方法 07/26修改
const getCurrentRow = (row) => {
  // Vue 3中不需要$forceUpdate，响应式数据会自动更新
  selrows.value = row;
  emit("singleSel", row); // 07/26修改
};

// 分页组件事件
const GetList = (data) => {
  queryParams.PageNum = data.page;
  queryParams.PageSize = data.limit;
  bindData();
};

// 加载列表
const bindData = () => {
  listLoading.value = true;
  request
    .post("/system/SYSM01B4/getPageList", JSON.stringify(queryParams))
    .then((response) => {
      if (response.data.code == 200) {
        lst.value = response.data.data.list;
        total.value = response.data.data.total;
      }
      listLoading.value = false;
    })
    .catch(() => {
      listLoading.value = false;
    });
};

// 查询
const search = (res) => {
  if (res != "") {
    queryParams.SearchPojo = { username: res };
  } else {
    delete queryParams.SearchPojo;
  }
  queryParams.SearchType = 1;
  queryParams.PageNum = 1;
  bindData();
};

const rowIndex = ({ row, rowIndex }) => {
  row.row_index = rowIndex;
};

const rowClick = (row) => {
  radio.value = row.row_index;
  getCurrentRow(row);
};
</script>
<style lang="scss" scoped>
.current-row {
  background: blue;
}
</style>
