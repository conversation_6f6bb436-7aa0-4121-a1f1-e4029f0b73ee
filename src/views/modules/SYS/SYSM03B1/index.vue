<template>
  <div>
    <div class="page-container">
      <div class="flex j-s" style="flex-wrap: nowrap">
        <div style="flex: 1">
          <listheader
            @btnAdd="addRole()"
            @btnSearch="search"
            @bindData="bindData"
            @btnExport="btnExport"
          />
          <el-table
            v-loading="listLoading"
            :data="lst"
            element-loading-text="Loading"
            border
            fit
            highlight-current-row
            style="overflow: auto"
            :header-cell-style="{
              background: '#F3F4F7',
              color: '#555',
              padding: '3px 0px 3px 0px',
            }"
            :cell-style="{ padding: '4px 0px 4px 0px' }"
            :height="tableMaxHeight"
            @row-click="rowClick"
          >
            <el-table-column align="center" label="ID" min-width="50">
              <template #default="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <template v-for="(i, index) in tableForm.item" :key="index">
              <el-table-column
                v-if="i.displaymark ? true : false"
                :prop="i.itemcode"
                :columnKey="i.itemcode"
                :label="i.itemname"
                :align="i.aligntype ? i.aligntype : 'center'"
                :min-width="i.minwidth"
                :show-overflow-tooltip="i.overflow ? true : false"
                :sortable="i.sortable ? true : false"
              >
                <template #default="scope">
                  <el-button
                    v-if="i.itemcode == 'rolecode'"
                    type="text"
                    size="small"
                    @click="editRole(scope.row)"
                  >
                    {{ scope.row[i.itemcode] ? scope.row[i.itemcode] : "角色编码" }}
                  </el-button>
                  <span v-else-if="i.itemcode == 'createdate'">
                    {{ formatDate(scope.row[i.itemcode]) }}
                  </span>
                  <div v-else-if="i.itemcode == 'power'">
                    <el-button
                      size="small"
                      type="text"
                      icon="el-icon-menu"
                      @click="handleWebMenu(scope.row)"
                    >
                      菜单
                    </el-button>
                    <el-button
                      size="small"
                      type="text"
                      icon="el-icon-mobile-phone"
                      @click="handleAppMenu(scope.row)"
                    >
                      APP
                    </el-button>
                    <el-button
                      size="small"
                      type="text"
                      icon="el-icon-s-flag"
                      @click="handlePower(scope.row)"
                    >
                      按钮
                    </el-button>
                  </div>
                  <div v-else-if="i.itemcode == 'operate'">
                    <el-button
                      size="small"
                      type="text"
                      icon="el-icon-delete"
                      style="color: #f56c6c"
                      @click="deleteForm(scope.row.roleid)"
                    >
                      删除
                    </el-button>
                  </div>
                  <span v-else>{{ scope.row[i.itemcode] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <Pagination
            :total="total"
            :page="queryParams.PageNum"
            :limit="queryParams.PageSize"
            @pagination="getList"
            @update:page="(val) => (queryParams.PageNum = val)"
            @update:limit="(val) => (queryParams.PageSize = val)"
          />
        </div>
        <div style="width: 600px; margin-top: 10px">
          <elitem ref="elitem" :lstitem="formdata.item" :formdata="formdata" style="width: 100%" />
        </div>
      </div>
    </div>

    <!-- 权限 -->
    <el-drawer v-model="powerVisible" :with-header="false" :size="'60%'" @closed="powerclose">
      <power ref="powerRef" :idx="idx" :functionData="powerdata" @closeBtn="powerclose" />
    </el-drawer>
    <!-- web -->
    <el-drawer v-model="webMenuVisible" :with-header="false" :size="'60%'" @closed="webMenuClose">
      <webMenu ref="webMenuRef" :idx="idx" :functionData="webmenudata" @closeBtn="webMenuClose" />
    </el-drawer>
    <!-- APP -->
    <el-drawer v-model="appMenuVisible" :with-header="false" :size="'60%'" @closed="appMenuClose">
      <appMenu ref="appMenuRef" :idx="idx" :functionData="appmenudata" @closeBtn="appMenuClose" />
    </el-drawer>
    <!-- 添加角色 -->
    <el-dialog
      v-if="RoleVisible"
      v-model="RoleVisible"
      title="添加角色"
      width="500px"
      :close-on-click-modal="false"
      top="10vh"
    >
      <el-form
        ref="formdataRef"
        :model="formdata"
        label-width="100px"
        class="custInfo"
        auto-complete="on"
        :rules="formRules"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="角色名称" prop="rolename">
              <el-input
                v-model="formdata.rolename"
                placeholder="请输入角色名称"
                clearable
                size="small"
                style="width: 100%; min-width: 140px !important"
                @input="writeCode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="角色编码" prop="rolecode">
              <el-input
                v-model="formdata.rolecode"
                placeholder="请输入角色编码"
                clearable
                size="small"
                style="width: 100%; min-width: 140px !important"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" size="small" @click="submitRoleBtn()">确 定</el-button>
          <el-button size="small" @click="RoleVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import listheader from "./components/listheader.vue";
import Pagination from "@/components/Pagination/index.vue";
import request from "@/utils/request";
import { tableTh } from "./components/tablecolums.js";
import CRUD from "./CRUD.JS";
import power from "./components/power/power.vue";
import webMenu from "./components/webMenu/index.vue";
import appMenu from "./components/appMenu/index.vue";
import elitem from "./components/elitem.vue";
import pinyin from "js-pinyin";

// let pinyin = require("js-pinyin");

// 响应式数据
const idx = ref(0);
const listLoading = ref(false);
const lst = ref([]); // 列表数据

// form验证内容
const RoleVisible = ref(false);
const formRules = reactive({
  rolecode: [{ required: true, trigger: "blur", message: "角色编码为必填项" }],
  rolename: [{ required: true, trigger: "blur", message: "角色名称为必填项" }],
});
const tableForm = reactive(tableTh);
const formdata = reactive({
  rolename: "",
  rolecode: "",
  item: [],
});
const total = ref(0);
const queryParams = reactive({
  PageNum: 1,
  PageSize: 10,
  OrderType: 1,
  SearchType: 1, // 高级搜索开启否 0为条件and   1为条件或者
});
const powerVisible = ref(false);
const powerdata = reactive({});
//web界面
const webMenuVisible = ref(false);
const webmenudata = reactive({});
//App界面
const appMenuVisible = ref(false);
const appmenudata = reactive({});

// 表单引用
const formdataRef = ref(null);
const elitemRef = ref(null);
const powerRef = ref(null);
const webMenuRef = ref(null);
const appMenuRef = ref(null);

// 计算属性
const tableMaxHeight = computed(() => {
  return window.innerHeight - 160 + "px";
});

// 日期格式化函数
const formatDate = (dataStr) => {
  if (!dataStr) return "";
  const dt = new Date(dataStr);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1).toString().padStart(2, "0");
  const d = dt.getDate().toString().padStart(2, "0");
  const hh = dt.getHours().toString().padStart(2, "0");
  const mm = dt.getMinutes().toString().padStart(2, "0");
  const ss = dt.getSeconds().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
};
// 方法
// 分页组件事件
const getList = (data) => {
  queryParams.PageNum = data.page;
  queryParams.PageSize = data.limit;
  bindData();
};

// 加载列表
const bindData = () => {
  listLoading.value = true;
  request
    .post("/system/SYSM03B1/getPageList", JSON.stringify(queryParams))
    .then((response) => {
      if (response.data.code == 200) {
        lst.value = response.data.data.list;
        total.value = response.data.data.total;
      } else {
        ElMessage.warning(response.data.msg || "查询失败");
      }
      listLoading.value = false;
    })
    .catch(() => {
      ElMessage.error("请求失败");
      listLoading.value = false;
    })
    .finally(() => {
      nextTick(() => {
        if (elitemRef.value) {
          elitemRef.value.lst = [];
        }
      });
    });
};

// 查询
const search = (res) => {
  if (res != "") {
    queryParams.SearchPojo = { rolename: res };
  } else {
    delete queryParams.SearchPojo;
  }
  queryParams.SearchType = 1;
  queryParams.PageNum = 1;
  bindData();
};
// 添加角色
const submitRoleBtn = () => {
  formdataRef.value?.validate((valid) => {
    if (valid) {
      if (formdata.roleid) {
        CRUD.update(formdata)
          .then((res) => {
            if (res.code == 200) {
              ElMessage.success("保存成功");
              RoleVisible.value = false;
              bindData();
            }
          })
          .catch(() => {
            ElMessage.warning("保存失败");
          });
      } else {
        CRUD.add(formdata)
          .then((res) => {
            if (res.code == 200) {
              ElMessage.success("保存成功");
              RoleVisible.value = false;
              bindData();
            }
          })
          .catch(() => {
            ElMessage.warning("保存失败");
          });
      }
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};

const addRole = () => {
  Object.assign(formdata, { rolename: "", rolecode: "" });
  RoleVisible.value = true;
};

// 编辑角色
const editRole = (row) => {
  Object.assign(formdata, row);
  RoleVisible.value = true;
};

// 删除
const deleteForm = (roleId) => {
  ElMessageBox.confirm("此操作将永久删除该记录, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      CRUD.delete(roleId)
        .then(() => {
          ElMessage.success("删除成功");
          bindData();
        })
        .catch((er) => {
          ElMessage.warning(er || "删除失败");
        });
    })
    .catch(() => {});
};

const rowClick = (row) => {
  elitemRef.value?.bindData(row);
};

const btnExport = () => {
  let tableFormCopy = JSON.parse(JSON.stringify(tableForm));
  tableFormCopy.item = tableFormCopy.item.slice(0, tableFormCopy.item.length - 2);
  // 这里需要根据实际的导出方法调整
  // $btnExport(lst.value, tableFormCopy, "角色表");
};
// =============================================自定义项==================================
const writeCode = (val) => {
  pinyin.setOptions({ checkPolyphone: false, charCase: 1 });
  formdata.rolecode = pinyin.getFullChars(val);
};

//打开权限
const handlePower = (row) => {
  idx.value = row.roleid;
  Object.assign(powerdata, row);
  powerVisible.value = true;
};

const powerclose = () => {
  const elitem = powerRef.value?.$refs.elitem;
  if (elitem?.recordList?.length > 0) {
    elitem.sendChangePower();
  }
  nextTick(() => {
    powerVisible.value = false;
  });
};

//打开Web菜单
const handleWebMenu = (row) => {
  idx.value = row.roleid;
  Object.assign(webmenudata, row);
  webMenuVisible.value = true;
};

const webMenuClose = () => {
  const elitem = webMenuRef.value?.$refs.elitem;
  if (elitem?.recordList?.length > 0) {
    elitem.sendChangePower();
  }
  nextTick(() => {
    webMenuVisible.value = false;
  });
};

//打开APP菜单
const handleAppMenu = (row) => {
  idx.value = row.roleid;
  Object.assign(appmenudata, row);
  appMenuVisible.value = true;
};

const appMenuClose = () => {
  const elitem = appMenuRef.value?.$refs.elitem;
  if (elitem?.recordList?.length > 0) {
    elitem.sendChangePower();
  }
  nextTick(() => {
    appMenuVisible.value = false;
  });
};

// 生命周期
onMounted(() => {
  bindData();
});
</script>

<style scoped>
.page-container {
  padding: 0;
  height: calc(100vh - 105px - 37px);
}
.cab :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}
.cab :deep(.is-horizontal) {
  display: none;
}
.table-container {
  border: 1px solid #ddd;
  border-top: 0;
  background: #f3f4f7;
}
.flex {
  display: flex;
}
.j-s {
  justify-content: space-between;
}
</style>
