<template>
  <div>
    <div :class="'filter-container flex j-s'">
      <div>
        <el-input
          v-model="strfilter"
          placeholder="请输入查询"
          prefix-icon="el-icon-search"
          style="width: 260px; height: 100%"
          class="filter-item"
          size="small"
          clearable
          @clear="btnSearch"
          @keyup.enter="btnSearch"
        >
          <template #append>
            <el-button class="filter-item" size="small" @click="btnSearch">搜索</el-button>
          </template>
        </el-input>
        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="primary"
          icon="el-icon-plus"
          plain
          size="small"
          @click="btnAdd"
        >
          添加
        </el-button>
      </div>

      <div class="iShowBtn">
        <el-button size="small" icon="el-icon-refresh-right" @click="bindData"></el-button>
        <el-button
          size="small"
          icon="el-icon-download"
          title="导出Excel"
          @click="$emit('btnExport')"
        ></el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

// 响应式数据
const strfilter = ref("");

// 定义emits
const emit = defineEmits(["advancedSearch", "btnAdd", "btnSearch", "bindData", "btnExport"]);

const btnAdd = () => {
  emit("btnAdd");
};

const btnSearch = () => {
  console.log(strfilter.value);
  emit("btnSearch", strfilter.value);
};

const bindData = () => {
  emit("bindData");
};
</script>
<style lang="scss" scoped>
.filter-container {
  margin: 0 10px;
  padding: 4px 10px;
  box-sizing: border-box;
  width: 98%;
  position: relative;
  align-items: flex-end;
}

.flex {
  display: flex;
  align-items: center;
}
.a-c {
  align-items: center;
}
.j-s {
  justify-content: space-between;
}
.iShowBtn {
  margin-right: 10px;
  cursor: pointer;
  i:hover,
  i:active {
    color: #409eff;
  }
}
</style>
