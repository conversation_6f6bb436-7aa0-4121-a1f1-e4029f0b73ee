<template>
  <div class="flex f-d-c form" style="height: 100%">
    <div class="table-position">
      <div class="accordion" style="overflow: auto; height: calc(100vh - 265px)">
        <template v-for="(b, bnum) in powerLst" :key="bnum">
          <div>
            <div class="groupTitle title" @click="b.isShow = !b.isShow">
              <el-checkbox v-model="b.isTrue" @change="changePower($event, b)">
                <span style="font-weight: bold">{{ b.navname }}</span>
              </el-checkbox>
              <i
                :class="b.isShow ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
                style="margin-right: 20px"
              ></i>
            </div>
            <div v-show="b.isShow" class="" style="padding: 4px 10px">
              <template v-for="(c, cnum) in b.children" :key="cnum">
                <div>
                  <div style="margin: 6px 0">
                    <el-checkbox v-model="c.isTrue" @change="changePower($event, c)">
                      <span style="font-weight: bold">{{ c.navname }}</span>
                    </el-checkbox>
                  </div>

                  <div class="dBody">
                    <template v-for="(d, dnum) in c.children" :key="dnum">
                      <div style="margin-bottom: 6px">
                        <el-checkbox v-model="d.isTrue" @change="changePower($event, d)">
                          {{ d.navname }}
                        </el-checkbox>
                      </div>
                    </template>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div style="margin-top: 6px; display: flex; align-items: center"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import request from "@/utils/request";

// Props
const props = defineProps(["formdata", "lstitem", "idx"]);

// 响应式数据
const lst = ref([]);
const powerLst = ref([]);
const recordList = ref([]); //记录修改的权限内容
const timer = ref(null); // 定时器

// 生命周期
onMounted(() => {
  lst.value = [];
  bindData();
});
// 方法
const bindData = async (isTemp) => {
  await request.get(`/system/SYSM05B2/getMenuWebListByRole?roleid=${props.idx}`).then((res) => {
    if (res.data.code == 200) {
      lst.value = res.data.data;
    }
    if (!isTemp) {
      bindTemp();
    }
  });
};

const bindTemp = () => {
  powerLst.value = [];
  request
    .get("/system/SYSM05B2/getMenuWebListByTen")
    .then((res) => {
      if (res.data.code == 200) {
        for (var i = 0; i < res.data.data.length; i++) {
          res.data.data[i].isTrue = false;
          res.data.data[i].isShow = true;
          for (var j = 0; j < lst.value.length; j++) {
            if (lst.value[j].navid == res.data.data[i].navid) {
              res.data.data[i].isTrue = true;
            }
          }
        }
        powerLst.value = changeFormat(res.data.data);
        console.log("powerLst.value", powerLst.value);
      } else {
        ElMessage.warning(res.data.msg || "获取权限失败");
      }
    })
    .catch((error) => {
      ElMessage.error(error || "请求错误");
    });
};
const changePower = (val, row) => {
  toggleChildren(row, val); // 联动子级
  toggleParent(row); // 联动父级
  var paramData = [
    {
      roleid: props.formdata.roleid,
      navid: row.navid,
      iscreate: row.isTrue ? 1 : 0,
    },
  ];
  if (row.children) {
    const allChildData = flattenPermissions(row.children, row.id);
    for (var i = 0; i < allChildData.length; i++) {
      var Item = allChildData[i];
      var objItem = {
        roleid: props.formdata.roleid,
        navid: Item.navid,
        iscreate: Item.isTrue ? 1 : 0,
      };
      paramData.push(objItem);
    }
  }
  // console.log(paramData, "paramData");
  // 拼接，去重
  recordList.value = uniqueObjectArrays(paramData, recordList.value, "navid");
  // return
  //如果 recordList 长度达到 20，立即发送请求
  if (recordList.value.length >= 20) {
    sendChangePower(); //发送请求
  } else {
    setTimer(); //5 秒后发送请求
  }
};
const sendChangePower = () => {
  if (recordList.value.length == 0) return;
  const batchSize = 20;
  const batch = recordList.value.slice(0, batchSize); // 获取前 20 条记录
  recordList.value = recordList.value.slice(batchSize); // 保留剩余的记录
  var paramObj = {
    roleid: props.formdata.roleid,
    delete: [],
    create: [],
  };
  for (var i = 0; i < batch.length; i++) {
    if (batch[i].iscreate) {
      paramObj.create.push(batch[i].navid);
    } else {
      paramObj.delete.push(batch[i].navid);
    }
  }
  request
    .post("/system/SYSM03B6/batchCreateDelete", JSON.stringify(paramObj))
    .then(() => {
      if (recordList.value.length > 0) {
        sendChangePower();
      }
    })
    .finally(() => {
      // 如果所有记录都已发送，清除定时器
      if (recordList.value.length === 0 && timer.value) {
        clearTimeout(timer.value);
        timer.value = null;
      }
    });
};

// 设置定时器
const setTimer = () => {
  if (timer.value) {
    clearTimeout(timer.value); // 清除之前的定时器
  }
  timer.value = setTimeout(() => {
    sendChangePower();
  }, 5 * 1000); // 5 秒后发送请求
};
//  联动子级
const toggleChildren = (item, checked) => {
  if (item.children) {
    item.children.forEach((child) => {
      child.isTrue = checked;
      toggleChildren(child, checked); // 递归处理子级
    });
  }
};

// 联动父级
const toggleParent = (item) => {
  if (item.parent) {
    const allChildrenChecked = item.parent.children.every((child) => child.isTrue);
    item.parent.isTrue = allChildrenChecked;
    toggleParent(item.parent); // 递归处理父级
  }
};

// 数据扁平化
const flattenPermissions = (permissions, parent = null) => {
  return permissions.reduce((acc, item) => {
    item.parent = parent;
    acc.push(item);
    if (item.children) {
      acc = acc.concat(flattenPermissions(item.children, item));
    }
    return acc;
  }, []);
};
// 数组去重+去除未改变
const uniqueObjectArrays = (arr1, arr2, key) => {
  const combined = [...arr1, ...arr2];
  const unique = [];
  const keys = new Set();

  combined.forEach((item) => {
    if (!keys.has(item[key])) {
      keys.add(item[key]);
      unique.push(item);
    }
  });

  return unique;
};

const changeFormat = (data) => {
  const result = [];
  if (!Array.isArray(data)) {
    return result;
  }
  data.forEach((item) => {
    delete item.children;
  });
  const map = {};
  data.forEach((item) => {
    map[item.navid] = item;
  });
  data.forEach((item) => {
    const parent = map[item.navpid];
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      result.push(item);
    }
  });
  return result;
};

// 组件销毁前清理
onBeforeUnmount(() => {
  // 组件销毁前，确保发送未完成的更改
  if (recordList.value.length > 0) {
    console.log("组件销毁前，确保发送未完成的更改");
    sendChangePower();
  }
  // 清除定时器
  if (timer.value) {
    clearTimeout(timer.value);
    timer.value = null;
  }
});

// 暴露给父组件的方法
defineExpose({
  recordList,
  sendChangePower,
});
</script>
<style lang="scss" scoped>
.table-position {
  position: relative;
  overflow: auto;
  height: 100%;
  width: 100%;
}
/**flex css  */
.flex {
  display: flex;
  flex-wrap: wrap;
}
.f-1 {
  flex: 1;
}

.f-d-c {
  flex-direction: column;
}

.j-c {
  justify-content: center;
}

.a-c {
  align-items: center;
}

.j-s {
  justify-content: space-between;
}

.j-start {
  justify-content: flex-start;
}
.atitle {
  line-height: 36px;
  padding: 0px 10px;
  margin-top: -10px;
}
.groupTitle {
  background: #eee;
  padding: 0px 10px;
  line-height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #ddd;
  cursor: pointer;
}
.dBody {
  display: flex;
  flex-wrap: wrap;
  div {
    width: 20%;
  }
}
</style>
