<template>
  <div style="width: 100%; box-sizing: border-box" class="component-container">
    <div style="top: 0px; width: 100%; z-index: 10" class="form_button flex a-c j-end p-r">
      <div class="button-container p-a" style="top: 0px; right: 30px">
        <el-button size="small" @click="$emit('closeBtn')">关 闭</el-button>
      </div>
    </div>
    <div style="padding: 20px 20px 20px 20px">
      <div
        class="form-border form-container shandow form_info flex f-d-c"
        style="width: 100%"
        :style="{ height: formcontainHeight }"
      >
        <div ref="form_main_info" class="form form-head p-r">
          <el-form
            ref="formdataRef"
            :model="formdata"
            :label-width="formLabelWidth"
            class="custInfo"
            auto-complete="on"
          >
            <p class="formTitle">{{ title }}</p>
            <el-row>
              <el-col :span="10">
                <el-form-item label="角色名称">
                  <el-input
                    v-model="formdata.rolename"
                    :readonly="true"
                    size="small"
                    style="width: 100%; min-width: 140px !important"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="角色编码">
                  <el-input
                    v-model="formdata.rolecode"
                    :readonly="true"
                    size="small"
                    style="width: 100%; min-width: 140px !important"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-divider />
        </div>
        <!-- ==========================表身================================= -->
        <div class="form-body form f-1">
          <elitem
            ref="elitemRef"
            :key="new Date().getTime()"
            :lstitem="formdata.item"
            :formdata="formdata"
            :idx="idx"
            style="width: 98%"
          />
        </div>
        <!-- ==================================表尾================================= -->

        <el-form :label-width="'80px'" class="form">
          <el-row>
            <el-col :span="5">
              <el-form-item label="创建人">
                <span v-show="formdata.createby" class="el-form-item__label">
                  {{ formdata.createby }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="创建日期">
                <span v-show="formdata.createdate" class="el-form-item__label">
                  {{ formatDate(formdata.createdate) }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="制表">
                <span v-show="formdata.lister" class="el-form-item__label">
                  {{ formdata.lister }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="修改日期">
                <span v-show="formdata.createdate" class="el-form-item__label">
                  {{ formatDate(formdata.modifydate) }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from "vue";
import elitem from "./elitem.vue";

// Props
const props = defineProps(["idx", "functionData"]);

// 响应式数据
const title = ref("数据权限");
const formdata = reactive({
  lister: JSON.parse(window.localStorage.getItem("getInfo")).realname,
  createby: JSON.parse(window.localStorage.getItem("getInfo")).realname,
  item: [],
});
const formLabelWidth = ref("100px");

// 表单引用
const formdataRef = ref(null);
const elitemRef = ref(null);

// 计算属性
const formcontainHeight = computed(() => {
  return window.innerHeight - 30 + "px";
});

// 日期格式化函数
const formatDate = (dataStr) => {
  if (!dataStr) return "";
  const dt = new Date(dataStr);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1).toString().padStart(2, "0");
  const d = dt.getDate().toString().padStart(2, "0");
  const hh = dt.getHours().toString().padStart(2, "0");
  const mm = dt.getMinutes().toString().padStart(2, "0");
  const ss = dt.getSeconds().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
};

// 监听器
watch(
  () => props.idx,
  () => {
    bindData();
  }
);

// 方法
// 加载列表
const bindData = () => {
  if (props.idx != 0) {
    Object.assign(formdata, props.functionData);
  }
};

// 生命周期
onMounted(() => {
  bindData();
});

// 暴露给父组件的方法
defineExpose({
  elitemRef,
});
</script>
<style scoped lang="scss">
//灰色背景
$bg-gray: #e2e2e2;
$bg-component: #f5f5f5;
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}
:deep(.is-horizontal) {
  display: none;
}
:deep(.custInfo .el-row) {
  margin-bottom: -20px;
  display: flex;
  flex-wrap: wrap;
}
/**修改错误提示 */
:deep(.el-form-item__error) {
  top: 0;
  background: #fff;
  height: 25px;
  margin-top: 5px;
  margin-left: 5px;
  margin-bottom: 5px;
  width: 120px;
  display: flex;
  align-items: center;
  // display: flex;
  // flex-wrap: wrap ;
}
// ::v-depp .el-input__inner{

// }
/**美化用  外框阴影*/
.shandow {
  box-shadow: 0px 0px 10px $bg-gray;
}
/**位置css */
.p-r {
  position: relative;
}
.p-a {
  position: absolute;
}
.p-f {
  position: fixed;
}
/**flex css  */
.flex {
  display: flex;
  flex-wrap: wrap;
}
.f-1 {
  flex: 1;
}

.f-d-c {
  flex-direction: column;
}

.j-c {
  justify-content: center;
}

.a-c {
  align-items: center;
}

.j-s {
  justify-content: space-between;
}

.j-start {
  justify-content: flex-start;
}
.j-end {
  justify-content: flex-end;
}
//顶部工具栏
.form_button {
  .button-container {
    background: $bg-gray;
    padding: 10px;
  }
}
//表格边框
.form-border {
  border: 2px solid #dbdbdb;
}
.component-container {
  background: $bg-component;
  height: calc(100vh - 84px);
  .form-container {
    background: #fff;
  }
  .form {
    margin-right: 20px;
    margin-left: 20px;
    width: calc(100% - 40px);
  }
  .form-head {
    //工具栏上下padding 加按键高度 -容器的上padding
    margin-top: calc(10px + 32px + 10px - 20px);
    .refNo {
      margin-right: 30px;
      min-width: 300px;
    }
  }
}
/**更改 表单label的字体格式 */
:deep(.el-form-item__label) {
  font-size: 12px;
  font-weight: 700;
}
.RoleHeader {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
  .Role-img {
    width: 100px;
    height: 80px;
    margin-right: 15px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .functionName {
    font-weight: bold;
    vertical-align: middle;
    font-size: 20px;
    color: #606266;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 8px 0;
    span {
      font-size: 16px;
    }
  }
  .description {
    margin-top: 4px;
    vertical-align: middle;
    font-size: 14px;
    color: #606266;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
</style>
