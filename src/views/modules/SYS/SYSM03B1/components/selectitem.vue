<template>
  <div>
    <div class="filter-container" style="margin-bottom: 5px">
      <el-input
        v-model="strfilter"
        placeholder="请输入查询"
        style="width: 200px"
        class="filter-item"
        size="small"
        @keyup.enter="search(strfilter)"
      />
      <el-button class="filter-item" type="primary" size="small" @click="search(strfilter)">
        查询
      </el-button>
    </div>
    <div>
      <el-table
        ref="selectValRef"
        v-loading="listLoading"
        :data="lst"
        element-loading-text="Loading"
        border
        fit
        height="400px"
        highlight-current-row
        style="overflow: auto"
        :header-cell-style="{
          background: '#F3F4F7',
          color: '#555',
          padding: '3px 0px 3px 0px',
        }"
        :cell-style="{ padding: '4px 0px 4px 0px' }"
      >
        <!-- ---复选列--- -->
        <el-table-column v-if="multi == 1" type="selection" width="40" />
        <!-- ---单选列--- -->
        <el-table-column v-else label="" width="35">
          <template #default="scope">
            <el-radio v-model="radio" :label="scope.$index" @change="getCurrentRow(scope.row)">
              {{ " " }}
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column label="角色编码" align="center" width="100px">
          <template #default="scope">
            <span>{{ scope.row.rolecode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="角色名" align="center" width="80px" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.rolename }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="制表"
          align="center"
          prop="mobile"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ scope.row.company ? scope.row.company : "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createdate"
          width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ formatDate(scope.row.createdate) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page="queryParams.PageNum"
      :limit="queryParams.PageSize"
      @pagination="GetList"
      @update:page="(val) => (queryParams.PageNum = val)"
      @update:limit="(val) => (queryParams.PageSize = val)"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import request from "@/utils/request";
import Pagination from "@/components/Pagination/index.vue";

// Emits
const emit = defineEmits(["singleSel"]);

// 响应式数据
const listLoading = ref(true);
const lst = ref([]); // 列表数据
const searchstr = ref(" "); // sql查询文本
const strfilter = ref(""); // 搜索框内输入的值
const total = ref(0);
const radio = ref(""); // 单选用参数默认为0即可
const selrows = ref(""); // 选择的内容 单选变量
const queryParams = reactive({
  PageNum: 1,
  PageSize: 10,
  OrderType: 1,
  SearchType: 1,
});

// 表单引用
const selectValRef = ref(null);

// 日期格式化函数
const formatDate = (dataStr) => {
  if (!dataStr) return "";
  const dt = new Date(dataStr);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1).toString().padStart(2, "0");
  const d = dt.getDate().toString().padStart(2, "0");
  const hh = dt.getHours().toString().padStart(2, "0");
  const mm = dt.getMinutes().toString().padStart(2, "0");
  const ss = dt.getSeconds().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
};
// 方法
//  单选列方法 07/26修改
const getCurrentRow = (row) => {
  nextTick(() => {
    selrows.value = row;
    emit("singleSel", row); // 07/26修改
  });
};

// 分页组件事件
const GetList = (data) => {
  queryParams.PageNum = data.page;
  queryParams.PageSize = data.limit;
  bindData();
};

// 加载列表
const bindData = () => {
  listLoading.value = true;
  request
    .post("/system/SYSM03B1/getPageList", JSON.stringify(queryParams))
    .then((response) => {
      if (response.data.code == 200) {
        lst.value = response.data.data.list;
        total.value = response.data.data.total;
      }
      listLoading.value = false;
    })
    .catch(() => {
      listLoading.value = false;
    });
};

// 查询
const search = (res) => {
  if (res != "") {
    queryParams.SearchPojo = { rolecode: res, rolename: res };
  } else {
    delete queryParams.SearchPojo;
  }
  queryParams.SearchType = 1;
  queryParams.PageNum = 1;
  bindData();
};

// 生命周期
onMounted(() => {
  searchstr.value = "";
  bindData();
});

// 暴露给父组件的属性和方法
defineExpose({
  selrows,
  selectValRef,
});
</script>
<style lang="scss" scoped>
.current-row {
  background: blue;
}
</style>
