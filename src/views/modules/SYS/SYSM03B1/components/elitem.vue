<template>
  <div class="flex f-d-c form" style="height: 100%">
    <div>
      <el-row>
        <el-button-group>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-circle-plus-outline"
            :disabled="!formdata.roleid"
            @click="openUserList()"
          >
            添 加
          </el-button>
          <el-button
            type="danger"
            size="small"
            icon="el-icon-delete"
            :disabled="!multipleSelection.length"
            @click="delItem()"
          >
            删 除
          </el-button>
        </el-button-group>
      </el-row>
    </div>
    <div class="f-1">
      <el-table
        ref="multipleTable"
        v-loading="listLoading"
        :data="lst"
        element-loading-text="Loading"
        border
        fit
        highlight-current-row
        size="small"
        class="tb-edit tableBox"
        style="overflow: auto; height: calc(100% - 30px)"
        :header-cell-style="{
          background: '#F3F4F7',
          color: '#555',
          padding: '4px 0px 4px 0px',
        }"
        :cell-style="{ padding: '6px 0px' }"
        :row-style="{ height: '20px' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40" />
        <el-table-column label="用户账号" align="center" min-width="60" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.username }}</span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center" min-width="60" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.realname }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="60" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ formatDate(scope.row.createdate) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div style="margin-top: 6px; display: flex; align-items: center"></div>
    <el-dialog
      v-if="userListVissable"
      v-model="userListVissable"
      title="用户信息"
      :append-to-body="true"
      width="1200px"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <selUser ref="selUserRef" :multi="1" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm()">确 定</el-button>
          <el-button @click="userListVissable = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import selUser from "@/views/modules/SYS/SYSM01B1/components/selectitem.vue";
import request from "@/utils/request";
import CRUD from "../CRUD.JS";

// 响应式数据
const listLoading = ref(false); // 列表加载中
const userListVissable = ref(false); // 选择商品对话框显示
const lst = ref([]);
const multipleSelection = ref([]);
const formdata = reactive({
  roleid: "",
});

// 表单引用
const multipleTableRef = ref(null);
const selUserRef = ref(null);

// 日期格式化函数
const formatDate = (dataStr) => {
  if (!dataStr) return "";
  const dt = new Date(dataStr);
  const y = dt.getFullYear();
  const m = (dt.getMonth() + 1).toString().padStart(2, "0");
  const d = dt.getDate().toString().padStart(2, "0");
  const hh = dt.getHours().toString().padStart(2, "0");
  const mm = dt.getMinutes().toString().padStart(2, "0");
  const ss = dt.getSeconds().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
};

// 方法
const bindData = (row) => {
  if (row) {
    if (row.roleid != formdata.roleid) {
      Object.assign(formdata, row);
    } else if (row.roleid == formdata.roleid) {
      // 防止重复点击
      return;
    }
  }
  lst.value = [];
  request
    .get(`/system/SYSM03B2/getListByRole?key=${formdata.roleid}`)
    .then((res) => {
      if (res.data.code == 200) {
        lst.value = res.data.data;
      } else {
        ElMessage.warning(res.data.msg || "查询失败");
      }
    })
    .catch((err) => {
      ElMessage.error(err || "请求失败");
    });
};
const submitForm = async () => {
  console.log(selUserRef.value?.$refs.selectVal.selection);
  const selectData = selUserRef.value?.$refs.selectVal.selection || [];
  let promiseList = []; // 异步请求列表
  for (let i = 0; i < selectData.length; i++) {
    let promise = new Promise((resolve, reject) => {
      formdata.userid = selectData[i].userid;
      formdata.username = selectData[i].username;
      formdata.realname = selectData[i].realname;
      formdata.usercode = selectData[i].usercode;
      CRUD.addItem(formdata)
        .then((res) => {
          if (res.code == 200) {
            resolve("保存成功");
          } else {
            reject("保存失败");
          }
        })
        .catch(() => {
          reject("保存失败");
        });
    });
    promiseList.push(promise);
  }
  await Promise.all(promiseList)
    .then(() => {
      ElMessage.success("保存成功");
    })
    .catch(() => {
      ElMessage.warning("保存失败");
    })
    .finally(() => {
      userListVissable.value = false;
      bindData();
    });
};
const openUserList = () => {
  if (!formdata.roleid) {
    ElMessage.warning("请先选择角色");
    return;
  }
  userListVissable.value = true;
};

// 多选操作方法
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
};

// 明细删除一项 不过首先要有弹出框询问 确定删除
const delItem = () => {
  ElMessageBox.confirm("此操作将永久删除该记录, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 执行删除的方法
      deleteRows();
    })
    .catch(() => {});
};
// 移除行数据
const deleteRows = async () => {
  const val = multipleSelection.value;
  // 如果选中数据存在
  if (val) {
    let promiseList = []; // 异步请求列表
    for (let item of val) {
      let promise = new Promise((resolve, reject) => {
        CRUD.deleteItem(item.id)
          .then((res) => {
            if (res.code == 200) {
              resolve("删除成功");
            } else {
              reject("删除失败");
            }
          })
          .catch(() => {
            reject("删除失败");
          });
      });
      promiseList.push(promise); // 每个异步请求push到列表中
    }
    await Promise.all(promiseList)
      .then(() => {
        // 等所有异步请求完成后执行
        ElMessage.success("删除成功");
      })
      .catch(() => {
        ElMessage.warning("删除失败");
      })
      .finally(() => {
        bindData();
        multipleSelection.value = [];
        multipleTableRef.value?.clearSelection();
      });
  }
};

// 暴露方法给父组件使用
defineExpose({
  bindData,
  lst,
});
</script>
