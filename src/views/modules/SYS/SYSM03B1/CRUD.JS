import request from "@/utils/request";
const CRUD = {

    // get user info 保存
    add(formdata) {
        return new Promise((resolve, reject) => {
            
            var params = JSON.stringify(formdata)
            request
                .post("/system/SYSM03B1/create", params) 
                .then((response) => {
                    console.log(params,response)
                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });
        })
    },
    // 更新添加
    update(formdata) {
        return new Promise((resolve, reject) => {
            
            var params = JSON.stringify(formdata);
            request
                .post("/system/SYSM03B1/update", params) 
                .then((response) => {
                    console.log(response,params);
                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });

        })
    },
    addItem(formdata) {
        return new Promise((resolve, reject) => {
            
            var params = JSON.stringify(formdata)
            request
                .post("/system/SYSM03B2/create", params) 
                .then((response) => {
                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });
        })
    },
    deleteItem(idx) {
        return new Promise((resolve, reject) => {
           request
           .get(`/system/SYSM03B2/delete?key=${idx}`) 
           .then((response) => {
               if (response.data.code == 200) {
                   resolve(response.data);
               } else {
                   reject(response.data.msg)
               }
           })
           .catch((error) => {
               reject(error)
           });
        })
    },
    delete(idx) {
        return new Promise((resolve, reject) => {
           request
           .get(`/system/SYSM03B1/delete?key=${idx}`) 
           .then((response) => {
               if (response.data.code == 200) {
                   resolve(response.data);
               } else {
                   reject(response.data.msg)
               }
           })
           .catch((error) => {
               reject(error)
           });
        })
    },
     addPowerItem(formdata) {
        return new Promise((resolve, reject) => {
            
            var params = JSON.stringify(formdata)
            request
                .post("/system/SYSM03B4/create", params) 
                .then((response) => {
                    if (response.data.code == 200) {
                        resolve(response.data);
                    } else {
                        reject(response.data.msg)
                    }
                })
                .catch((error) => {
                    reject(error)
                });
        })
    },
    deletePowerItem(idx) {
        return new Promise((resolve, reject) => {
           request
           .get(`/system/SYSM03B4/delete?key=${idx}`) 
           .then((response) => {
               if (response.data.code == 200) {
                   resolve(response.data);
               } else {
                   reject(response.data.msg)
               }
           })
           .catch((error) => {
               reject(error)
           });
        })
    },
}

export default CRUD