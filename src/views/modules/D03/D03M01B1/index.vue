<script setup lang="ts">
import { useUserStoreHook } from "@/store/modules/user.store";
import request from "@/utils/request";

async function refreshToken() {
  await useUserStoreHook().refreshToken();
}
async function pageList() {
  request({
    url: `/system/SYSM03B2/getListByRole?key=1916020269168721920`,
    method: "get",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}
</script>

<template>
  <div>
    报价单
    <el-button type="primary" @click="refreshToken">刷新token</el-button>
    <el-button type="primary" @click="pageList">接口测试</el-button>
  </div>
</template>
<style lang="scss" scoped></style>
