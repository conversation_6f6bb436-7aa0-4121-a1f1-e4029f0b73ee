<script setup lang="ts">
import { getMicroState, checkProxyMicro } from "@/micro";

const state = getMicroState();

const microStateList = ref([]);

const checkState = () => {
  microStateList.value.forEach((item) => {
    item.state = 3;
  });

  checkProxyMicro(true);
};

onMounted(() => {
  checkState();
});

watch(
  state,
  (val) => {
    microStateList.value = Object.values(val);
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<template>
  <div class="state-container">
    <el-button type="primary" size="small" class="check-btn" @click="checkState">
      检测微应用
    </el-button>
    <el-table size="small" :data="microStateList" border>
      <el-table-column label="ID" prop="key"></el-table-column>
      <el-table-column label="地址" prop="url"></el-table-column>
      <el-table-column label="状态" prop="state">
        <template #default="scope">
          <el-tag v-if="scope.row.state === 0" type="info">未知</el-tag>
          <el-tag v-if="scope.row.state === 1" type="success">正常</el-tag>
          <el-tag v-if="scope.row.state === 2" type="danger">异常</el-tag>
          <el-tag v-if="scope.row.state === 3" type="warning">检测中</el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss">
.state-container {
  padding: 10px;
  width: 100%;
  .check-btn {
    margin-bottom: 10px;
  }
}
</style>
