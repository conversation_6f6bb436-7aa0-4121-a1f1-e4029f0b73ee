<template>
  <div class="profileBody">
    <el-row :gutter="10" type="flex">
      <el-col :span="6">
        <div class="proInfo">
          <el-card class="box-card">
            <template #header>
              <div class="clearfix">
                <span>个人信息</span>
              </div>
            </template>
            <div class="text item">
              <div class="img-content">
                <div class="img-content-box" @click="changePhoto">
                  <img
                    :src="
                      formdata.avatar
                        ? 'data:image/jpeg;base64,' + formdata.avatar
                        : 'https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg'
                    "
                    alt=""
                  />
                </div>
              </div>
              <div class="detail-content">
                <div class="proInfo-item">
                  <span>用户账号</span>
                  <span>{{ listForm.username }}</span>
                </div>
                <div class="proInfo-item">
                  <span>手机号码</span>
                  <span>{{ listForm.mobile || "-" }}</span>
                </div>
                <div class="proInfo-item">
                  <span>用户邮箱</span>
                  <span>{{ listForm.email || "-" }}</span>
                </div>
                <div class="proInfo-item">
                  <span>所属{{ userStore.appConfig?.model }}</span>
                  <span>{{ listForm.tenantname || "-" }}</span>
                </div>
                <div class="proInfo-item">
                  <span>公司名称</span>
                  <span>{{ listForm.company || "-" }}</span>
                </div>
                <div class="proInfo-item">
                  <span>公司地址</span>
                  <span>{{ listForm.companyadd || "-" }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="baseInfo" style="height: 100%">
          <el-card class="box-card" style="height: 100%">
            <template #header>
              <div class="clearfix">
                <span>基本资料</span>
              </div>
            </template>
            <div class="text item">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="基本资料" name="first">
                  <el-form
                    ref="formdataRef"
                    :model="formdata"
                    label-width="100px"
                    class="custInfo"
                    auto-complete="on"
                  >
                    <el-row>
                      <el-col :span="24">
                        <el-form-item label="昵称" prop="realname">
                          <!-- :readonly=true -->
                          <el-input
                            v-model="formdata.realname"
                            placeholder="请输入用户昵称"
                            clearable
                            style="min-width: 140px !important"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="手机号码" prop="mobile">
                          <el-input
                            v-model="formdata.mobile"
                            placeholder="请输入手机号码"
                            clearable
                            style="min-width: 140px !important"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="邮箱" prop="email">
                          <el-input
                            v-model="formdata.email"
                            placeholder="请输入邮箱"
                            clearable
                            style="min-width: 140px !important"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="24">
                        <el-form-item label="性别">
                          <el-radio v-model="formdata.sex" :label="0">男</el-radio>
                          <el-radio v-model="formdata.sex" :label="1">女</el-radio>
                        </el-form-item>
                      </el-col>
                      <el-col :span="22" :offset="2">
                        <el-button
                          type="primary"
                          style="background-color: #1890ff"
                          size="small"
                          @click="saveBaseInfo()"
                        >
                          保 存 信 息
                        </el-button>
                        <el-button
                          type="danger"
                          style="background-color: #ff4949"
                          size="small"
                          @click="closeTag()"
                        >
                          关 闭
                        </el-button>
                      </el-col>
                    </el-row>
                  </el-form>
                </el-tab-pane>
                <el-tab-pane label="修改密码" name="second">
                  <el-form
                    ref="pwdformdataRef"
                    :model="pwdformdata"
                    :rules="pwdformdataRule"
                    label-width="100px"
                    class="custInfo"
                    auto-complete="on"
                    style="min-height: 200px"
                  >
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="原密码" prop="originalpassword">
                          <div style="position: relative">
                            <el-input
                              v-model="pwdformdata.originalpassword"
                              placeholder="请输入原密码"
                              :type="passwordType1"
                            />
                            <span class="show-pwd" @click="showPwd('passwordType1')">
                              <svg-icon
                                :icon-class="passwordType1 === 'password' ? 'eye' : 'eye-open'"
                              />
                            </span>
                          </div>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="新密码" prop="password">
                          <div style="position: relative">
                            <el-input
                              v-model="pwdformdata.password"
                              placeholder="请输入新密码"
                              :type="passwordType"
                            />
                            <span class="show-pwd" @click="showPwd('passwordType')">
                              <svg-icon
                                :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"
                              />
                            </span>
                          </div>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="确认密码" prop="oldpassword">
                          <div style="position: relative">
                            <el-input
                              v-model="pwdformdata.oldpassword"
                              placeholder="请重新输入密码"
                              :type="passwordType2"
                            />
                            <span class="show-pwd" @click="showPwd('passwordType2')">
                              <svg-icon
                                :icon-class="passwordType2 === 'password' ? 'eye' : 'eye-open'"
                              />
                            </span>
                          </div>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="22" :offset="2">
                        <el-button
                          type="primary"
                          style="background-color: #1890ff"
                          size="small"
                          @click="changPwd()"
                        >
                          修 改 密 码
                        </el-button>
                        <el-button size="small" @click="resetBtn('pwdformdata')">重置</el-button>
                      </el-col>
                    </el-row>
                  </el-form>
                </el-tab-pane>
                <el-tab-pane label="个性化" name="third">
                  <div class="dash">
                    <!-- <div v-for="(i,index) in dashlist" :key="index">
                        {{i.dashname}}
                      </div> -->
                    <el-form label-width="100px">
                      <el-form-item label="工作台">
                        <el-select
                          v-model="dashValue"
                          placeholder="请选择工作台"
                          style="width: 260px"
                          clearable
                          @change="changeDash"
                          @clear="
                            dashValue = '';
                            dashRow = {};
                          "
                        >
                          <el-option
                            v-for="item in dashlist"
                            :key="item.id"
                            :label="item.dashname"
                            :value="item.id"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item label="钉钉">
                        <span style="margin-right: 10px">
                          {{ dingInfo.id ? "已绑定 " + dingInfo.nickname : "未绑定" }}
                        </span>
                        <el-button
                          v-if="!dingInfo.id"
                          type="primary"
                          plain
                          size="small"
                          @click="justbinding()"
                        >
                          绑定
                        </el-button>
                        <el-button v-else type="warning" plain size="small" @click="deleteDing()">
                          解绑
                        </el-button>
                      </el-form-item>
                      <el-form-item label="企业">
                        <span style="margin-right: 10px">
                          {{ dingInfo.id ? "已绑定 " + dingInfo.nickname : "未绑定" }}
                        </span>
                        <el-button
                          v-if="!dingInfo.id"
                          type="primary"
                          plain
                          size="small"
                          @click="justwechart()"
                        >
                          绑定
                        </el-button>
                        <el-button
                          v-else
                          type="warning"
                          plain
                          size="small"
                          @click="deletewechart()"
                        >
                          解绑
                        </el-button>
                      </el-form-item>
                      <el-form-item label="授权码">
                        <span style="margin-right: 10px">
                          {{ authorizeInfo.id ? "已授权" : "未授权" }}
                        </span>
                        <el-button
                          v-if="!authorizeInfo.id"
                          type="primary"
                          plain
                          size="small"
                          @click="justauthorize()"
                        >
                          授权
                        </el-button>
                        <el-button
                          v-else
                          type="warning"
                          plain
                          size="small"
                          @click="deleteauthorize()"
                        >
                          解除授权
                        </el-button>
                        <el-button
                          type="primary"
                          plain
                          size="small"
                          @click="getAuthorizeInfo(true)"
                        >
                          查看授权码
                        </el-button>
                      </el-form-item>
                      <el-form-item>
                        <el-button type="primary" size="small" @click="saveDashboard()">
                          保 存
                        </el-button>
                        <el-button size="small" @click="getworkbenchInfo()">刷 新</el-button>
                      </el-form-item>
                    </el-form>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>
    <div v-show="false">
      <input ref="uploadRef" type="file" @change="getFile" />
    </div>
    <el-dialog
      v-model="isErcode"
      title="二维码"
      :append-to-body="true"
      width="500px"
      :close-on-click-modal="false"
      @closed="closed()"
    >
      <!-- @load="loaded" -->
      <iframe
        id="codeiframe"
        ref="iframe"
        :src="dingdingcodeUrl"
        frameborder="0"
        style="width: 100%; height: 500px"
      ></iframe>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useTagsViewStore, useUserStore } from "@/store";
import { ElMessage, ElMessageBox } from "element-plus";
import request from "@/utils/request";
import { isWeakPassword } from "@/utils/auth";
import lrz from "lrz";

// 获取store、router、route实例
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();

// 响应式数据
const activeName = ref("first");
const formdata = reactive({});
const pwdformdata = reactive({
  password: "",
  oldpassword: "",
  originalpassword: "",
});
const listForm = reactive({});
const passwordType = ref("password");
const passwordType1 = ref("password");
const passwordType2 = ref("password");
const dashlist = ref([]); // 工作台
const dashValue = ref("");
const dashRow = reactive({});
// 钉钉绑定
const dingdingcodeUrl = ref("");
const dingInfo = reactive({
  id: "",
});
const wechartInfo = reactive({
  id: "",
});
const authorizeInfo = reactive({
  id: "",
});
const isErcode = ref(false);

// 表单引用
const formdataRef = ref(null);
const pwdformdataRef = ref(null);
const uploadRef = ref(null);

// 验证函数
const validatePass2 = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请再次输入密码"));
  } else if (value !== pwdformdata.password) {
    callback(new Error("两次输入密码不一致!"));
  } else {
    callback();
  }
};

const validatePass = (rule, value, callback) => {
  if (!value) {
    callback(new Error("密码为必填项"));
    return;
  }
  isWeakPassword(value).then((isWeek) => {
    if (isWeek) {
      callback(new Error("密码强度较弱"));
    } else {
      callback();
    }
  });
};

// 表单验证规则
const pwdformdataRule = reactive({
  originalpassword: [
    { required: true, trigger: "blur", message: "原密码为必填项" },
    { min: 6, trigger: "blur", message: "密码不能少于 6 位" },
  ],
  password: [
    { min: 6, trigger: "blur", message: "密码不能少于 6 位" },
    { required: true, validator: validatePass, trigger: "blur" },
  ],
  oldpassword: [
    { min: 6, trigger: "blur", message: "密码不能少于 6 位" },
    { required: true, validator: validatePass2, trigger: "blur" },
  ],
});

// 计算属性
const userinfo = computed(() => userStore.userInfo);
// 方法
const bindData = async () => {
  const uid = JSON.parse(window.localStorage.getItem("inks-userinfo")).userid;
  const Tenantid = JSON.parse(window.localStorage.getItem("inks-userinfo")).tenantid;

  await request.get(`/system/SYSM01B3/getEntity?key=${Tenantid}`).then((response) => {
    if (response.data.code == 200) {
      for (const key in response.data.data) {
        listForm[key] = response.data.data[key];
      }
    }
  });

  await request.get(`/system/SYSM01B1/getEntity?key=${uid}`).then((response) => {
    if (response.data.code == 200) {
      Object.assign(formdata, response.data.data);
      for (const key in response.data.data) {
        listForm[key] = response.data.data[key];
      }
    }
  });
};

const saveBaseInfo = () => {
  request.post("/system/SYSM01B1/update", JSON.stringify(formdata)).then((res) => {
    console.log("saveBaseInfo", res);
    if (res.data.code == 200) {
      ElMessage.success("个人信息修改成功");
    } else {
      ElMessage.warning("个人信息修改失败");
    }
    bindData();
  });
};

// 关闭
const closeTag = () => {
  const tagsViewStore = useTagsViewStore();
  tagsViewStore.delView(route);
  router.push({ path: "/" });
};

// 重置
const resetBtn = (val) => {
  if (val === "pwdformdata") {
    pwdformdataRef.value?.resetFields();
  } else {
    formdataRef.value?.resetFields();
  }
};
// 密码重置
const changPwd = () => {
  pwdformdataRef.value?.validate((valid) => {
    if (valid) {
      request
        .get(
          "/system/SYSM01B2/changePwd?op=" +
            pwdformdata.originalpassword +
            "&np=" +
            pwdformdata.password
        )
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            ElMessage.success(res.data.msg || "密码修改成功");
          } else {
            ElMessage.warning(res.data.msg || "密码重置失败");
          }
        });
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};

const showPwd = (val) => {
  nextTick(() => {
    if (val === "passwordType") {
      passwordType.value = passwordType.value === "password" ? "" : "password";
    } else if (val === "passwordType1") {
      passwordType1.value = passwordType1.value === "password" ? "" : "password";
    } else if (val === "passwordType2") {
      passwordType2.value = passwordType2.value === "password" ? "" : "password";
    }
  });
};
const getFile = () => {
  const inputDOM = uploadRef.value;
  const file = inputDOM.files[0];
  const fileType = [".png", ".PNG", ".jpg", ".JPG"];
  const realSize = (parseFloat(file.size) / 1024 / 1024).toFixed(2);

  const hzIndex = file.name.lastIndexOf(".");
  const nameLen = file.name.length;
  // 获取后缀名
  const hz = file.name.substring(hzIndex, nameLen);
  if (fileType.includes(hz)) {
    lrz(file)
      .then(function (rst) {
        console.log(rst.fileLen / 1024 / 1024, rst);
        const retSize = (rst.fileLen / 1024 / 1024).toFixed(2);
        if (retSize > 0.4) {
          ElMessage.warning(`请上传小于2MB的图片，此图片${realSize}MB`);
          return;
        } else {
          const imgBase64 = rst.base64.split("data:image/jpeg;base64,")[1];
          formdata.avatar = imgBase64;
          saveBaseInfo();
        }
      })
      .catch(function (err) {
        console.log("压缩失败了", err);
      })
      .always(function () {
        console.log("压缩成功");
      });
  } else {
    ElMessage.warning(`请添加正确的图片格式`);
  }
};

// 修改头像
const changePhoto = () => {
  uploadRef.value.click();
};
const handleClick = (tab) => {
  if (tab.label == "个性化") {
    getDingInfo();
    getWeChartInfo();
    getAuthorizeInfo();
  }
};

const changeDash = (val) => {
  const index = dashlist.value.findIndex((item) => item.id == val);
  if (index === -1) {
    return;
  }
  const row = dashlist.value[index];
  Object.assign(dashRow, {
    name: row.dashname,
    value: row.mvcurl,
    dashid: row.id,
  });
};

// 保存用户-工作台
const saveDashboard = () => {
  const obj = {
    cfgkey: "system.style.dashboard",
    cfgvalue: JSON.stringify(dashRow),
  };

  request.post("/system/SYSM06B3/setConfig", JSON.stringify(obj)).then((res) => {
    console.log(res);
    if (res.data.code == 200) {
      ElMessage.success(res.data.msg || "保存成功");
      showWorkbench();
    } else {
      ElMessage.warning(res.data.msg || "保存失败");
    }
  });
};
// 获取 工作台信息列表
const getworkbenchInfo = () => {
  request.get("/system/SYSM02B2/getDashListBySelf").then((res) => {
    if (res.data.code == 200) {
      dashlist.value = res.data.data;
      const data = userStore.userInfo?.configs?.["system.style.dashboard"];
      if (data) {
        const objdata = JSON.parse(data);
        dashValue.value = objdata.dashid;
        changeDash(dashValue.value);
      } else {
        dashValue.value = "";
      }
    }
  });
};

const showWorkbench = () => {
  request.get("/system/SYSM06B3/getConfigValue?key=system.style.dashboard").then((res) => {
    if (res.data.code == 200) {
      if (res.data.data == null || res.data.data == "") {
        userStore.configs["system.style.dashboard"] = "";
      } else {
        userStore.configs["system.style.dashboard"] = res.data.data;
      }
    }
  });
};

const closed = () => {
  getDingInfo();
  getWeChartInfo();
  getAuthorizeInfo();
};
// 绑定企业微信
const justwechart = () => {
  let baseApi;
  const inksApp = localStorage.getItem("inks-appconfig");
  if (inksApp) {
    baseApi = JSON.parse(inksApp)?.baseURL || "";
  }
  dingdingcodeUrl.value =
    baseApi + "auth/justbinding/render/wechat_enterprise/" + userinfo.value.token;
  console.log(dingdingcodeUrl);
  nextTick(() => {
    isErcode.value = true;
  });
};

const getWeChartInfo = () => {
  request
    .get(
      "/system/SYSM01B7/getJustauthByUserid?key=" +
        userinfo.value.userid +
        "&type=wechat_enterprise"
    )
    .then((res) => {
      console.log(res, "wechat_enterprise");
      if (res.data.code == 200) {
        if (res.data.data == null) {
          Object.assign(wechartInfo, {});
          return;
        }
        Object.assign(wechartInfo, res.data.data);
      } else {
        Object.assign(wechartInfo, {});
      }
    });
};
const deletewechart = () => {
  ElMessageBox.confirm("是否确认解绑企业微信?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      request
        .get("/system/SYSM01B7/delete?key=" + wechartInfo.id)
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            ElMessage.success(res.data.msg || "解绑成功");
            getWeChartInfo();
          } else {
            ElMessage.warning(res.data.msg || "解绑失败");
          }
        })
        .catch((er) => {
          ElMessage.error(er || "请求错误");
        });
    })
    .catch(() => {});
};
//绑定钉钉
const justbinding = () => {
  let baseApi;
  const inksApp = localStorage.getItem("inks-appconfig");
  if (inksApp) {
    baseApi = JSON.parse(inksApp)?.baseURL || "";
  }
  dingdingcodeUrl.value = baseApi + "auth/justbinding/render/dingtalk/" + userinfo.value.token;
  nextTick(() => {
    isErcode.value = true;
  });
};

const getDingInfo = () => {
  request
    .get("/system/SYSM01B7/getJustauthByUserid?key=" + userinfo.value.userid + "&type=ding")
    .then((res) => {
      console.log(res);
      if (res.data.code == 200) {
        if (res.data.data == null) {
          Object.assign(dingInfo, {});
          return;
        }
        Object.assign(dingInfo, res.data.data);
      } else {
        Object.assign(dingInfo, {});
      }
    });
};
const deleteDing = () => {
  ElMessageBox.confirm("是否确认解绑钉钉?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      request
        .get("/system/SYSM01B7/delete?key=" + dingInfo.id)
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            ElMessage.success(res.data.msg || "解绑成功");
            getDingInfo();
          } else {
            ElMessage.warning(res.data.msg || "解绑失败");
          }
        })
        .catch((er) => {
          ElMessage.error(er || "请求错误");
        });
    })
    .catch(() => {});
};
// 授权码
const justauthorize = () => {
  let baseurl = "/system/SYSM01B2/createSecret";
  if (authorizeInfo.id) {
    baseurl = "/system/SYSM01B2/refreshSecret";
  }
  request
    .get(baseurl)
    .then((res) => {
      console.log(res);
      if (res.data.code == 200) {
        if (res.data.data == null) {
          Object.assign(authorizeInfo, { id: "" });
          return;
        }
        authorizeInfo.id = res.data.data;
      } else {
        ElMessage.warning(res.data.msg || "用户授权失败");
      }
    })
    .catch((er) => {
      ElMessage.error(er || "请求错误");
    });
};
const getAuthorizeInfo = (isTrue) => {
  request
    .get("/system/SYSM01B2/getSecret")
    .then((res) => {
      console.log("getSecret", res);
      if (res.data.code == 200) {
        if (res.data.data == null) {
          Object.assign(authorizeInfo, { id: "" });
          return;
        }
        authorizeInfo.id = res.data.data;
        if (isTrue) {
          let html = "";
          html += "<div style='font-weight:bold;font-size:16px'>" + authorizeInfo.id + "</div>";
          html += "<div style='color:#f44336'>注：授权码仅当前账号使用，不可以给予他人使用</div>";
          ElMessageBox.confirm(html, "用户授权码", {
            cancelButtonText: "取消",
            confirmButtonText: "复制",
            type: "info",
            dangerouslyUseHTMLString: true,
          })
            .then((res) => {
              if (res == "confirm") {
                const cInput = document.createElement("input");
                cInput.value = authorizeInfo.id;
                document.body.appendChild(cInput);
                cInput.select(); // 选取文本框内容
                document.execCommand("copy");
                document.body.removeChild(cInput);
              }
            })
            .catch(() => {});
        }
      } else {
        ElMessage.warning(res.data.msg || "获取授权码失败");
      }
    })
    .catch((er) => {
      ElMessage.error(er || "请求错误");
    });
};
const deleteauthorize = () => {
  ElMessageBox.confirm("是否确认解除授权码?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      request
        .get("/system/SYSM01B2/deleteSecret")
        .then((res) => {
          console.log(res);
          if (res.data.code == 200) {
            ElMessage.success(res.data.msg || "解除授权成功");
            authorizeInfo.id = "";
          } else {
            ElMessage.warning(res.data.msg || "解除授权失败");
          }
        })
        .catch((er) => {
          ElMessage.error(er || "请求错误");
        });
    })
    .catch(() => {});
};

// 生命周期
onMounted(() => {
  bindData();
  getworkbenchInfo();
});
</script>
<style scoped>
.profileBody {
  margin: 20px;
}
.img-content {
  width: 100%;
  height: 124px;
}
.img-content-box {
  width: 120px;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
  cursor: pointer;
  background: #000;
  position: relative;
}
.img-content-box img:hover {
  opacity: 0.6;
}
.img-content-box img {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 9;
}
.img-content-box::after {
  content: "+";
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 28px;
  opacity: 0;
  transform: translate(-50%, -50%);
  z-index: 8;
}
.img-content-box:hover::after {
  opacity: 1;
}
.detail-content {
  margin-top: 20px;
}
.detail-content .proInfo-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  /* border-bottom: 1px solid #e7eaec; */
  border-top: 1px solid #e7eaec;
  font-size: 14px;
}
.detail-content .proInfo-item span:first-child {
  font-weight: bold;
}
.show-pwd {
  position: absolute;
  right: 10px;
}
</style>
