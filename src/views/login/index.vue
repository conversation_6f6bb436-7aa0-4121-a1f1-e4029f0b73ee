<!--
 * @file index.vue
 * @description 登录页面
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-22 caoshi 修改页面结构、加入handleLoginSubmit登录所需接口参数、加入租户选择
 * 2025-04-23 caoshi 菜单导航改为usePermissionStore
 * 2025-04-23 caoshi 新增键盘处理
 * 2025-04-24 caoshi inksoms改为appconfig读取
 * 2025-04-25 caoshi 页面格式修改、加入rekey
-->
<script setup lang="ts">
import { ElMessage } from "element-plus";
import { type LoginFormData } from "@/api/auth.api";
import type { FormInstance } from "element-plus";
import { useAppStore, useUserStore, usePermissionStore } from "@/store";
import request from "@/utils/request";
import { getRefreshToken, setAccessToken, setRtKey } from "@/utils/auth";
import AuthAPI from "@/api/auth.api";
import { onMounted, onBeforeUnmount, ref, watch } from "vue";
import QRCode from "qrcodejs2-fix";
import router from "@/router";
import SetTrackingLog from "@/utils/commonLog";
import loginImage from "../../assets/images/login-image.png";
import { RefreshLeft } from "@element-plus/icons-vue";
import { microClear } from "@/micro";
// import { ca } from "element-plus/es/locale";
const userStore = useUserStore();
const appStore = useAppStore();
const permissionStore = usePermissionStore();
const codeNum = ref(0);
// const route = useRoute();
const loginFormRef = ref<FormInstance>();

const loading = ref(false); // 按钮 loading 状态
const isCapslock = ref(false); // 是否大写锁定
// const captchaBase64 = ref(); // 验证码图片Base64字符串
const uuid = ref("");
const codeUrl = ref("");
// 初始化 tenantList 为 Tenant 类型数组
const tenantList = ref<Tenant[]>([]);
const tenantRefs = ref<(HTMLElement | null | ComponentPublicInstance | Element)[]>([]);
//租户切换
let loginTenant = ref(true);
let tenantidActive = ref("");
let dialogVisible = ref(false);

const activeIndex = ref(tenantList.value.findIndex((i) => i.tenantItemActive));

// 更新激活项
const updateActive = () => {
  tenantList.value.forEach((item, idx) => {
    item.tenantItemActive = idx === activeIndex.value;
  });

  // 滚动到可视区域
  scrollToActiveItem();
};
const scrollToActiveItem = () => {
  const el = tenantRefs.value[activeIndex.value];
  if (el instanceof Element) {
    (el as HTMLElement).scrollIntoView({ behavior: "smooth", block: "nearest" });
  }
};
// 键盘处理
const handleKeyDown = (e: KeyboardEvent) => {
  if (tenantList.value.length === 0) return;
  if (e.key === "ArrowDown" && loginTenant.value === false) {
    activeIndex.value = (activeIndex.value + 1) % tenantList.value.length;
    updateActive();
  } else if (e.key === "ArrowUp" && loginTenant.value === false) {
    activeIndex.value = (activeIndex.value - 1 + tenantList.value.length) % tenantList.value.length;
    updateActive();
  } else if (e.key === "Enter") {
    if (loginTenant.value === false) {
      submitTenant();
    } else {
      handleLoginSubmit();
    }
    // handleLoginSubmit();
    // submitTenant();
  } else if (e.key === "Escape" && loginTenant.value === false) {
    logout();
  }
};

const eulaTitle = ref("");
let appconfig = localStorage.getItem("inks-appconfig");
if (appconfig) {
  appconfig = JSON.parse(appconfig);
  const eula = appconfig.eula;
  switch (eula) {
    case "inkstech":
      eulaTitle.value = "Copyright@嘉兴应凯科技有限公司 版权所有";
      break;
    case "service":
      eulaTitle.value = "嘉兴应凯科技有限公司 技术支持";
      break;
    case "na":
      eulaTitle.value = "";
      break;
    default:
      eulaTitle.value = "Copyright@嘉兴应凯科技有限公司 版权所有";
      break;
  }
}

// 监听器挂载
onMounted(() => {
  window.addEventListener("keydown", handleKeyDown);
  microClear();
});

onBeforeUnmount(() => {
  window.removeEventListener("keydown", handleKeyDown);
});

// 保持 activeIndex 和 tenantList 同步（防止外部切换）
watch(
  () => tenantList.value,
  () => {
    activeIndex.value = tenantList.value.findIndex((i) => i.tenantItemActive);
  },
  { deep: true }
);

const loginFormData = ref<LoginFormData>({
  username: "",
  password: "",
  captchaKey: "",
  captchaCode: "",
});

const bg = ref("");

// 监听appStore config.env=dev时进行默认输入
watch(
  () => appStore.config,
  (newVal) => {
    if (newVal) {
      if (newVal.loginimg) {
        bg.value = newVal.loginimg;
      } else {
        bg.value = loginImage;
      }
      if (newVal.env === "dev") {
        loginFormData.value.username = "demo";
        loginFormData.value.password = "123456";
      }
    }
  },
  { deep: true, immediate: true }
);

const loginRules = computed(() => {
  return {
    username: [
      {
        required: true,
        trigger: "blur",
        message: "请输入用户名",
      },
    ],
    password: [
      {
        required: true,
        trigger: "blur",
        message: "请输入密码",
      },
      {
        min: 6,
        message: "密码长度不能小于6位",
        trigger: "blur",
      },
    ],
    captchaCode: [
      {
        required: true,
        trigger: "blur",
        message: "请输入验证码",
      },
    ],
  };
});

// 登录
async function handleLoginSubmit() {
  if (loginFormRef.value) {
    SetTrackingLog("login_submit", loginFormData.value.username, "");
    try {
      await loginFormRef.value.validate(); // 调用 validate 方法并等待其完成
      if (codeNum.value >= 2) {
        try {
          const res = await AuthAPI.chkCaptcha(loginFormData.value.captchaCode, uuid.value);
          if (res.data.code === 200) {
            await handleLogin();
          } else {
            await getCode();
            loginFormData.value.captchaCode = "";
            ElNotification({
              title: "登录失败",
              type: "warning",
              message: res.data.msg || "验证码错误，请重试",
            });
          }
        } catch (err: any) {
          console.error("验证码验证失败", err);
          ElMessage.warning(err?.message || "验证码验证失败");
        }
      } else {
        handleLogin();
      }
    } catch (error) {
      console.error("表单验证失败:", error);
      ElMessage.warning(error || "验证失败");
    }
  }
}

async function handleLogin() {
  loading.value = true;
  try {
    const res: any = await userStore.login(loginFormData.value);
    if (res.data.code === 200) {
      ElNotification({
        title: "登录成功",
        type: "success",
        message: "欢迎登录",
      });
      codeNum.value = 0;
      loginFormData.value.captchaCode = "";
      // 拉取租户列表
      await getTenant();
    } else {
      ElNotification({
        title: "登录失败",
        type: "warning",
        message: res.data.msg || "登录失败，请重试",
      });
      SetTrackingLog("login_fail", res.data.msg, "/SaUser/login");
    }
  } catch (err: any) {
    console.error("登录异常", err);
    ElNotification({
      title: "登录异常",
      type: "error",
      message: err?.message || "登录过程中发生异常",
    });
    SetTrackingLog("login_fail", err.message, "/SaUser/login");
  } finally {
    codeNum.value++;
    if (loginTenant.value && codeNum.value >= 2) {
      getCode();
    }
    loading.value = false;
  }
}

// 定义租户数据结构
interface Tenant {
  tenantid: string;
  name: string;
  tenantName?: string;
  tenantItemActive?: boolean;
  company?: string;
}

// 获取租户列表
async function getTenant() {
  loginTenant.value = false;
  try {
    const response = await AuthAPI.getTenant();
    if (response.data.code === 200) {
      tenantList.value = response.data.data;
      if (tenantList.value.length > 0) {
        for (var i = 0; i < tenantList.value.length; i++) {
          tenantList.value[i].tenantItemActive = false;
        }
        tenantList.value[0].tenantItemActive = true;
        tenantidActive.value = tenantList.value[0].tenantid;
      }
    } else {
      ElMessage.warning("租户获取失败，请重新获取");
    }
  } catch (error) {
    ElMessage.warning(error || "请求错误");
  }
}
async function getCode() {
  try {
    const res = await AuthAPI.getCaptcha();
    if (res.data.code === 200) {
      codeUrl.value = res.data.img;
      uuid.value = res.data.uuid;
    } else {
      ElMessage.warning("验证码获取失败，请重新获取");
    }
  } catch (error) {
    ElMessage.warning(error || "请求错误");
  }
}

/**
 * 解析 redirect 字符串 为 path 和  queryParams
 *
 * @returns { path: string, queryParams: Record<string, string> } 解析后的 path 和 queryParams
 */
// function parseRedirect(): {
//   path: string;
//   queryParams: Record<string, string>;
// } {
//   const query: LocationQuery = route.query;
//   const redirect = (query.redirect as string) ?? "/";

//   const url = new URL(redirect, window.location.origin);
//   const path = url.pathname;
//   const queryParams: Record<string, string> = {};

//   url.searchParams.forEach((value, key) => {
//     queryParams[key] = value;
//   });

//   return { path, queryParams };
// }

// 检查输入大小写
function checkCapslock(event: KeyboardEvent) {
  // 防止浏览器密码自动填充时报错
  if (event instanceof KeyboardEvent) {
    isCapslock.value = event.getModifierState("CapsLock");
  }
}
//加载pinia中的appconfig
const pageTitle = computed(() => appStore.config.title ?? "嘉兴应凯");
function dialogController() {
  dialogVisible.value = true;
}

function changeTenantItemActive(index: any, id: string) {
  for (var i = 0; i < tenantList.value.length; i++) {
    tenantList.value[i].tenantItemActive = false;
  }
  tenantList.value[index].tenantItemActive = !tenantList.value[index].tenantItemActive;
  tenantidActive.value = id;
}
async function submitTenant() {
  loading.value = true;
  if (!tenantidActive.value) {
    ElMessage.warning("暂无租户选择");
    return;
  }
  try {
    const res = await request.post("/auth/tokenRtKey", {
      tenantid: tenantidActive.value,
      refreshtoken: getRefreshToken(),
      fncode: appStore.config.deffncode,
      first: "true",
    });

    if (res.data.code === 200) {
      setAccessToken(res.data.data.access_token);
      setRtKey(res.data.data.rtkey);
      userStore.setUserInfo(res.data.data.loginuser);
      localStorage.removeItem("inks-fncode");
      localStorage.removeItem("inks-menu");

      await request
        .get(`/system/SYSM06B1/getConfigValue?key=platform.theme.mastercolor&tid=default`)
        .then((res) => {
          if (res.data.code === 200) {
            let masterColor = "#366cb3";
            if (res.data.data) {
              masterColor = res.data.data;
            }
            localStorage.setItem("inks-master-color", masterColor);
            const el = document.documentElement;
            el.style.setProperty("--inks-master-color", masterColor);
          }
        });

      // 拉取菜单
      const currentTenant = tenantList.value.find((v) => v.tenantid === tenantidActive.value);
      userStore.getTenantInfo(currentTenant);
      readnav();
      SetTrackingLog("login_success", "", "/SaUser/login");
    } else {
      ElMessage.warning(res.data.msg || "暂无租户选择");
    }
  } catch (error) {
    console.error("请求失败:", error);
    ElMessage.error("请求失败，请稍后重试");
  } finally {
    loading.value = false;
  }
}
//菜单读取
function readnav() {
  var baseurl = "/system/SYSM02B2/getMenuWebListBySelf";
  if (appStore.config.deffncode) {
    baseurl += "?fncode=" + appStore.config.deffncode;
  }
  request.get(baseurl).then((response) => {
    if (response.data.code == 200) {
      // 将导航写入状态
      var navjson = response.data.data;
      permissionStore.setNavData(navjson);
      showWorkbench();
    } else {
      ElMessage.warning(response.data.msg || "获取菜单失败");
    }
  });
}
function showWorkbench() {
  router.push("/dashboard");
  // var data: any = userStore.userInfo.configs;
  // if (!data["system.style.dashboard"]) {
  //   router.push("/");
  // } else {
  //   let objdata;
  //   try {
  //     objdata = JSON.parse(data["system.style.dashboard"]);
  //   } catch (e) {
  //     console.error("dashboard 配置格式错误:", e);
  //     router.push("/");
  //     return;
  //   }
  // }
}

function logout() {
  userStore.logout().then(() => {});
  loginTenant.value = !loginTenant.value;
  getCode();
  ElNotification({
    title: "账号已安全退出",
    message: dateFormat(),
    type: "success",
  });
  loading.value = false;
}
function dateFormat() {
  var dt = new Date();
  var y = dt.getFullYear();
  var m = (dt.getMonth() + 1).toString().padStart(2, "0");
  var d = dt.getDate().toString().padStart(2, "0");
  var hh = dt.getHours().toString().padStart(2, "0");
  var mm = dt.getMinutes().toString().padStart(2, "0");
  var ss = dt.getSeconds().toString().padStart(2, "0");
  return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
}
// 设置登录凭证
// const setLoginCredentials = (username: string, password: string) => {
//   loginFormData.value.username = username;
//   loginFormData.value.password = password;
// };
const isErcode = ref(true);
const iscodeError = ref(false);
function openErcode() {
  nextTick(() => {
    isErcode.value = false;
  });

  document.getElementById("qrcode").innerHTML = "";
  var time_stamp = parseInt(new Date().getTime() / 1000);
  new QRCode("qrcode", {
    render: "canvas",
    width: 200,
    height: 200,
    text: "http://www.inks100.com/login.html?logincode=" + time_stamp,
  });

  setTimeout(
    function () {
      document.getElementById("qrcode").innerHTML = "";
      new QRCode("qrcode", {
        render: "canvas",
        width: 200,
        height: 200,
        text: "二维码过期，请重新生成",
        colorDark: "#999",
        colorLight: "#FFF",
      });
      nextTick(() => {
        iscodeError.value = true;
      });
    },
    2 * 60 * 1000
  );
}

onMounted(() => {
  // getCaptcha();
  // loadAppJson(); // 加载 app.json 数据
});
</script>

<template>
  <div class="login-container flex a-c j-end" style="width: 100%; height: 100%">
    <div
      class="p-r"
      style="display: flex; align-items: center; justify-content: center; width: 100%; height: 100%"
    >
      <div class="logoBox" :style="{ backgroundImage: `url(${bg})` }"></div>
      <div v-if="loginTenant" class="login">
        <!-- 登录页头部 -->
        <div class="login-header"></div>
        <!-- 登录页内容 -->
        <el-form ref="loginFormRef" :model="loginFormData" :rules="loginRules" class="login-form">
          <div class="form-title">
            <h2>{{ pageTitle }}</h2>
          </div>
          <div v-show="isErcode">
            <div style="padding: 0 0 20px 0; font-size: 20px; text-align: center">
              <span style="color: #6285f7">账号密码</span>
              登录
            </div>
            <div class="ercode_tab swicth-ercode" @click="openErcode()">
              <svg
                width="52"
                height="52"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                fill="currentColor"
              >
                <defs>
                  <path id="id-3938311804-a" d="M0 0h48a4 4 0 0 1 4 4v48L0 0z" />
                </defs>
                <g fill="none" fill-rule="evenodd">
                  <mask id="id-3938311804-b" fill="#fff">
                    <use xlink:href="#id-3938311804-a" />
                  </mask>
                  <use fill="#0084FF" xlink:href="#id-3938311804-a" />
                  <image
                    width="52"
                    height="52"
                    mask="url(#id-3938311804-b)"
                    xlink:href="data:image/png;base64,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"
                  />
                </g>
              </svg>
            </div>
            <!-- 用户名 -->
            <el-form-item prop="username">
              <el-input
                v-model="loginFormData.username"
                size="large"
                placeholder="手机和邮箱"
                name="username"
              >
                <template #prefix>
                  <svg-icon icon-class="user" style="margin-left: 12px" />
                </template>
              </el-input>
            </el-form-item>
            <!-- 密码 -->
            <el-tooltip :visible="isCapslock" content="大写锁定已打开" placement="right">
              <el-form-item prop="password">
                <el-input
                  v-model="loginFormData.password"
                  placeholder="密码"
                  type="password"
                  name="password"
                  size="large"
                  class="h-[48px] pr-2"
                  show-password
                  @keyup="checkCapslock"
                  @keyup.enter="handleLoginSubmit"
                >
                  <template #prefix>
                    <svg-icon icon-class="password" style="margin-left: 12px" />
                  </template>
                </el-input>
              </el-form-item>
            </el-tooltip>

            <div v-if="codeNum >= 2" class="code-container">
              <el-form-item prop="captchaCode">
                <div class="code-input">
                  <span class="svg-container">
                    <svg-icon icon-class="code" />
                  </span>
                  <el-input
                    v-model="loginFormData.captchaCode"
                    type="text"
                    placeholder="验证码"
                    auto-complete="off"
                    @keyup.enter.native="handleLoginSubmit"
                  />
                </div>
                <div class="code-img">
                  <div v-if="!codeUrl" class="codeTip" @click="getCode">
                    <i class="el-icon-refresh-right"></i>
                    刷新验证码
                  </div>
                  <img
                    v-else
                    :src="'data:image/jpg;base64,' + codeUrl"
                    alt=""
                    width="117"
                    height="49"
                    @click="getCode"
                  />
                </div>
              </el-form-item>
            </div>

            <div class="flex-x-between w-full py-1">
              <el-checkbox>记住密码</el-checkbox>

              <el-link type="primary" href="/forget-password">忘记密码</el-link>
            </div>

            <!-- 登录按钮 -->
            <el-button
              :loading="loading"
              type="primary"
              size="large"
              class="w-full"
              @click.prevent="handleLoginSubmit"
            >
              登录
            </el-button>
          </div>
          <div v-show="!isErcode">
            <div style="font-size: 20px; text-align: center; padding: 0 0 20px 0">
              <span style="color: #6285f7">微信扫码</span>
              一键登录
            </div>
            <div class="ercode_tab switch-input" @click="isErcode = true">
              <svg
                width="52"
                height="52"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                fill="currentColor"
              >
                <defs>
                  <path id="id-14580708-a" d="M0 0h48a4 4 0 0 1 4 4v48L0 0z" />
                </defs>
                <g fill="none" fill-rule="evenodd">
                  <mask id="id-14580708-b" fill="#fff">
                    <use xlink:href="#id-14580708-a" />
                  </mask>
                  <use fill="#0084FF" xlink:href="#id-14580708-a" />
                  <path
                    fill="#FFF"
                    d="M22.125 4h13.75A4.125 4.125 0 0 1 40 8.125v27.75A4.125 4.125 0 0 1 35.875 40h-13.75A4.125 4.125 0 0 1 18 35.875V8.125A4.125 4.125 0 0 1 22.125 4zm6.938 34.222c1.139 0 2.062-.945 2.062-2.11 0-1.167-.923-2.112-2.063-2.112-1.139 0-2.062.945-2.062 2.111 0 1.166.923 2.111 2.063 2.111zM21 8.333v24h16v-24H21z"
                    mask="url(#id-14580708-b)"
                  />
                  <g mask="url(#id-14580708-b)">
                    <path
                      fill="#FFF"
                      d="M46.996 15.482L39 19.064l-7.996-3.582A1.6 1.6 0 0 1 32.6 14h12.8a1.6 1.6 0 0 1 1.596 1.482zM47 16.646V24.4a1.6 1.6 0 0 1-1.6 1.6H32.6a1.6 1.6 0 0 1-1.6-1.6v-7.754l8 3.584 8-3.584z"
                    />
                    <path
                      fill="#0084FF"
                      d="M31 15.483v1.17l8 3.577 8-3.577v-1.17l-8 3.581z"
                      fill-rule="nonzero"
                    />
                  </g>
                </g>
              </svg>
            </div>
            <div class="ercode" style="width: 200px; margin: 0 auto; position: relative">
              <div id="qrcode" ref="qrCode" />
            </div>
            <div v-if="iscodeError" class="codeError" @click="openErcode()">
              <el-icon><RefreshLeft /></el-icon>
              二维码失效，请重新生成
            </div>
            <div v-else class="ercode-foot">
              <div>
                打开
                <span>App</span>
              </div>
              <div style="font-size: 12px; margin-top: 10px">在「首页」右上角打开扫一扫</div>
            </div>
          </div>
          <span
            style="padding: 32px 0; line-height: 24px; color: #0000007a; text-align: center"
            data-local-value="noAccountTip"
          >
            还没有账户？
            <br />
            请联系管理员添加账户
          </span>
          <div
            style="
              position: absolute;
              bottom: 8px;
              font-size: 14px;
              color: #666;
              width: 100%;
              text-align: center;
              left: 0;
            "
          >
            {{ eulaTitle }}
          </div>
        </el-form>
      </div>
      <div v-else class="selectTenant" style="z-index: 99">
        <div class="title-container">
          <h3 class="title">选择账套</h3>
          <div class="titleFun">
            <span @click="dialogController()">注册</span>
            <span @click="getTenant()">刷新</span>
          </div>
        </div>
        <div v-if="tenantList.length != 0" class="tenantList">
          <template v-for="(i, index) in tenantList" :key="index">
            <div
              :ref="(el) => (tenantRefs[index] = el)"
              :class="
                i.tenantItemActive ? 'tenantList-item tenantList-item-active' : 'tenantList-item'
              "
              @click="changeTenantItemActive(index, i.tenantid)"
              @dblclick="submitTenant()"
            >
              <div class="select-none">
                <div>账套：{{ i.tenantName }}</div>
                <div>公司：{{ i.company }}</div>
              </div>
            </div>
          </template>
        </div>
        <div v-else class="noData">暂无账套</div>
        <div class="btnSubmit">
          <el-button
            :loading="loading"
            type="primary"
            size="large"
            style="width: 65%"
            @click="submitTenant()"
          >
            确 认
          </el-button>
          <el-button style="width: 25%" size="large" @click="logout">返回</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */
.p-r {
  position: relative;
}
.logoBox {
  width: 500px;
  height: 600px;
  overflow: hidden;
  background-position: center;
  background-size: 500px 600px;
  border-radius: 10px 0 0 10px;
}
$bg_dark: #2d3a4b; //新修改
$bg_light: #fbf9fe; //新修改
$light_gray: #fff;
$cursor: #fff;
$input_bg: #fff; //新添加
$back-color: #409eff;
$ui-cloor: #409eff;
$text-white: #fff;
@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}
/* reset element-ui css */
.login-container {
  background: linear-gradient(135deg, #e3e3e3, #f2f2f2, #d7d7d7, #e2e2e2);
  background-size: 100% 100%;

  .el-input {
    width: 100%;
    height: 47px;
    margin: auto;

    input {
      height: 47px;
      padding: 12px 5px 12px 15px;
      color: $bg_dark; //新修改
      caret-color: $bg_dark; //输入光标的颜色
      background: transparent;
      border: none;
      border-radius: 0px;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $light_gray inset !important; //新修改
        -webkit-text-fill-color: $bg_dark !important;
      }
    }
  }

  .el-form-item {
    color: #454545;
    // border: 1px solid rgba(0, 0, 0, 0.2); //修改
    background: $input_bg;
    border-radius: 5px;
  }
}

.selectTenant {
  position: relative;
  width: 400px;
  height: 600px;
  padding-bottom: 30px;
  overflow: hidden;
  background: #fff;
  background-size: cover;
  border-radius: 0 10px 10px 0;

  .title-container {
    position: relative;

    .title {
      margin: 15px auto 30px auto;
      font-size: 26px;
      font-weight: bold;
      color: $bg_dark; //修改
      text-align: center;
    }

    .titleFun {
      position: absolute;
      top: 24px;
      right: 0;
      z-index: 999;
      padding: 0px 10px;
      margin-top: 10px;
      text-align: right;

      span {
        margin: 0 4px;
        font-size: 15px;
        font-weight: bold;
        color: $bg_dark;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        user-select: none;
      }

      span:hover {
        color: #409eff;
      }
    }
  }

  .tenantList {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    justify-content: space-between;
    height: calc(100% - 130px);
    padding: 10px;
    overflow-y: auto;

    .tenantList-item {
      display: flex;
      align-items: center;
      width: 100%;
      height: 60px;
      padding: 10px 20px;
      margin-bottom: 8px;
      cursor: pointer;
      border: 1px solid #9e9e9e;
    }

    .tenantList-item-active {
      background: #409eff;
    }
  }

  .noData {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 26px;
    color: #9e9e9e;
    text-align: center;
    transform: translate(-50%, -50%);
  }

  .btnSubmit {
    position: absolute;
    bottom: 00px;
    width: 100%;
    padding: 10px;
    text-align: center;
    background: #fff;
    box-shadow:
      0 -1px 3px 0 rgb(0 0 0 / 12%),
      0 0 3px 0 rgb(0 0 0 / 4%);
  }
}

.login {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400px;
  height: 600px;
  background: #fff;
  background-size: cover;
  border-radius: 0 10px 10px 0;

  .login-header {
    position: absolute;
    top: 0;
    display: flex;
    justify-content: right;
    width: 100%;
    padding: 15px;

    .logo {
      width: 26px;
      height: 26px;
    }

    .title {
      margin: auto 5px;
      font-size: 24px;
      font-weight: bold;
      color: #3b82f6;
    }
  }

  .login-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 400px;
    height: 800px;
    padding: 30px;
    overflow: hidden;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: var(--el-box-shadow-light);

    @media (width <= 460px) {
      width: 100%;
      padding: 20px;
    }

    .form-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 0 20px;
      text-align: center;
    }

    .input-wrapper {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .captcha-img {
      height: 48px;
      cursor: pointer;
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }
  }

  .login-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 10px 0;
    text-align: center;
  }
}

:deep(.el-form-item) {
  background: var(--el-input-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 5px;
}

:deep(.el-input) {
  .el-input__wrapper {
    padding: 0;
    background-color: transparent;
    box-shadow: none;

    &.is-focus,
    &:hover {
      box-shadow: none !important;
    }

    input:-webkit-autofill {
      /* 通过延时渲染背景色变相去除背景颜色 */
      transition: background-color 1000s ease-in-out 0s;
    }
  }
}
.code-container {
  display: flex;
  :deep(.el-form-item) {
    border: unset;
    width: 100%;
    .el-form-item__content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }
  }
  .code-img {
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .codeTip {
      width: 117px;
      height: 49px;
    }
  }
  .code-input {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--el-border-color);
    border-radius: 5px;
    .svg-container {
      margin: 0 5px 0 12px;
    }
  }
}
.ercode_tab {
  position: absolute;
  right: 0;
  top: 0;
}
.ercode-foot {
  text-align: center;
  margin-top: 5px;
}
.codeError {
  width: 80%;
  text-align: center;
  margin: 0 auto;
  color: #409eff;
  font-weight: 700;
  background: hsla(0, 0%, 100%, 0.658);
  padding: 20px 30px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
