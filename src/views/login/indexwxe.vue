<template>
    <div>
        <!--后端：登录并选择好租户，返回token（包含用户和租户信息） ；
            前端：根据getLoginUser+key=token接口 获取用户信息并保存+获取菜单信息+跳转页面 -->
    </div>
</template>
<script>
import request from "@/utils/request";
import { getToken, setToken, removeToken } from "@/utils/auth";
export default {
  data() {
    return {
      value: "",
      value1: "",
      zhi: "",
      isloading: true,
    };
  },
  created() {},
  mounted() {
    this.Bindata();
  },
  methods: {
    Bindata() {
      this.value = window.location.href?window.location.href:'';
        this.value1 = this.value.split("?key=")[1];
      request
        .get("/auth/wxelogin/getLoginUser?key=" + this.value1)
        .then((res) => {
          if (res.data.code == 200) {
            var getInfo = res.data.data.loginuser;
            localStorage.setItem("getInfo", JSON.stringify(getInfo));
            this.$store.state.user.userinfo = getInfo; //存储用户信息
            var token = res.data.data.access_token;
            setToken(token);
            this.$store.state.user.token = token; //存储Token
            // 获取菜单
            this.readnav();
          } else {
            this.$confirm("获取用户信息失败，是否返回登录？", "提示", {
              confirmButtonText: "返回登录",
              cancelButtonText: "退出系统",
              type: "warning",
              closeOnClickModal:false
            })
              .then((res) => {
                if (res === "confirm") {
                  // removeToken();
                  this.$router.push("/login");
                  this.$store.dispatch("user/logout");
                } else {
                   window.location.href="about:blank";
                    window.close();
                }
              }).catch(() => {
                  window.location.href="about:blank";
                window.close();
              });
          }
        }).catch(err=>{
            this.$confirm("获取用户信息失败，是否返回登录？", "提示", {
              confirmButtonText: "返回登录",
              cancelButtonText: "退出系统",
              type: "warning",
              closeOnClickModal:false,
            })
              .then((res) => {
                  console.log(res)
                if (res === "confirm") {
                  removeToken();
                  this.$router.push("/login");
                  this.$store.dispatch("user/logout");
                } else {
                //   window.location.href="about:blank";
                    window.close();
                }
              }).catch(() => {
                //   window.location.href="about:blank";
                window.close();
              });
        })
     
    },
    readnav() {
      request
        .get("/system/SYSM02B2/getMenuWebListBySelf")  
        .then((response) => {
          if (response.data.code == 200) {
            // 将导航写入状态
            var navjson = response.data.data;
            this.isloading = false; //关闭等待页面
            localStorage.setItem("navjson", JSON.stringify(navjson));
            this.$store.dispatch("app/setnavdata", navjson);
            // this.$router.push({ path: this.redirect || "/" });
            this.showWorkbench(); //工作台
          } else {
              this.$confirm("获取服务菜单失败，是否重试？", "提示", {
              confirmButtonText: "重试",
              cancelButtonText: "退出系统",
              type: "warning",
              closeOnClickModal:false
            })
              .then((res) => {
                if (res === "confirm") {
                 this.readnav();
                } else {
                  window.close();
                }
              }).catch(() => {
                window.close();
              });
          }
        })
        .catch((error) => {
         
        });
    },
    // 工作台
    showWorkbench() {
      // console.log('this', this.$store.state.user)
      var data=this.$store.state.user.userinfo.configs['system.style.dashboard']
      if(data==null||data == ""){
        this.$router.push({ path: this.redirect || "/" });
      }else{
         var objdata = JSON.parse(data);
          this.$router.push(objdata.value);
      }
    },
  },
};
</script>