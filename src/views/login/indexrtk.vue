<template>
  <div></div>
</template>
  <script>
import {
  getToken,
  setToken,
  setrtKey,
  setRefreshToken,
  removeToken,
} from "@/utils/auth";
import store from "@/store";
import request from "@/utils/request";
import Cookies from "js-cookie";
export default {
  data() {
    return {
      isloading: true,
    };
  },
  created() {},
  mounted() {
    request.defaults.baseURL = Cookies.get("baseApi",{domain:window.location.hostname});
    this.binData();
  },
  methods: {
    async binData() {
      let value = window.location.href ? window.location.href : "";
      let urlParams = value.split("?rt=")[1];
      if (!urlParams) {
        return;
      }
      try {
        const res = await request.post("/auth/authByRtKey", {
          rtkey: urlParams,
        });
        if (res.data.code === 200) {
          const getInfo = res.data.data.loginuser;
          localStorage.setItem("getInfo", JSON.stringify(getInfo)); //存储用户信息到本地local
          setToken(res.data.data.access_token); //存储短期token到cookie

          store.commit("user/SET_USERINFO", res.data.data.loginuser); //存储用户信息到store
          store.commit("user/SET_TOKEN", getToken()); //存储短期token到store
          store.commit(
            "user/SET_TENANTINFO",
            res.data.data.loginuser.tenantinfo
          ); //存储租户信息
          setrtKey(res.data.data.rtkey); //存储rtkey到cookie
          setRefreshToken(res.data.data.refreshtoken); //存储刷新token到cookie
          this.readnav();
        } else {
          this.$notify.error({
            title: "登录已到期，请重新登录",
            message: res.data.msg || "请重新登录",
            duration: 3500,
          });
          removeToken();
          this.$router.push({ path: "/login" });
          window.close();
        }
      } catch (error) {
        this.$notify.error({
          title: "登录已到期，请重新登录",
          message: "请重新登录",
          duration: 3500,
        });
        removeToken();
        this.$router.push({ path: "/login" });
        window.close();
      }
    },
    readnav() {
      // 从后台重新获取菜单，登陆时，最好不要从localStorage，获取旧菜单
      var baseurl = "/system/SYSM02B2/getMenuWebListBySelf";
      if (!!this.$store.state.app.config.deffncode) {
        // 单租户多服务默认菜单
        baseurl += "?fncode=" + this.$store.state.app.config.deffncode;
      }
      request
        .get(baseurl)
        .then((response) => {
          if (response.data.code == 200) {
            // 将导航写入状态
            var navjson = response.data.data;
            this.isloading = false; //关闭等待页面
            localStorage.setItem("navjson", JSON.stringify(navjson));
            this.$store.dispatch("app/setnavdata", navjson);
            this.$router.push({ path: "/" });
          }
        })
        .catch((error) => {});
    },
  },
};
</script>