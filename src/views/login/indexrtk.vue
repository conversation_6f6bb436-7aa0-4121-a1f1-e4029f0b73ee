<template>
  <div></div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElNotification } from "element-plus";
import { useRouter } from "vue-router";
import { useUserStore, useAppStore, usePermissionStore } from "@/store";
import {
  setAccessToken,
  setRtKey,
  setRefreshToken,
  clearToken,
} from "@/utils/auth";
import request from "@/utils/request";

// Stores
const userStore = useUserStore();
const appStore = useAppStore();
const permissionStore = usePermissionStore();
const router = useRouter();

// Reactive data
const isloading = ref(true);

// Lifecycle
onMounted(() => {
  // request.defaults.baseURL = Cookies.get("baseApi", { domain: window.location.hostname });
  binData();
});
// Methods
const binData = async () => {
  const value = window.location.href ? window.location.href : "";
  const urlParams = value.split("?rt=")[1];
  if (!urlParams) {
    return;
  }
  try {
    const res = await request.post("/auth/authByRtKey", {
      rtkey: urlParams,
    });
    if (res.data.code === 200) {
      const getInfo = res.data.data.loginuser;
      localStorage.setItem("getInfo", JSON.stringify(getInfo)); //存储用户信息到本地local
      setAccessToken(res.data.data.access_token); //存储短期token

      userStore.setUserInfo(res.data.data.loginuser); //存储用户信息到store
      userStore.getTenantInfo(res.data.data.loginuser.tenantinfo); //存储租户信息
      setRtKey(res.data.data.rtkey); //存储rtkey
      setRefreshToken(res.data.data.refreshtoken); //存储刷新token
      readnav();
    } else {
      ElNotification.error({
        title: "登录已到期，请重新登录",
        message: res.data.msg || "请重新登录",
        duration: 3500,
      });
      clearToken();
      router.push({ path: "/login" });
      window.close();
    }
  } catch (error) {
    ElNotification.error({
      title: "登录已到期，请重新登录",
      message: "请重新登录",
      duration: 3500,
    });
    clearToken();
    router.push({ path: "/login" });
    window.close();
  }
};
const readnav = () => {
  // 从后台重新获取菜单，登陆时，最好不要从localStorage，获取旧菜单
  let baseurl = "/system/SYSM02B2/getMenuWebListBySelf";
  if (appStore.config.deffncode) {
    // 单租户多服务默认菜单
    baseurl += "?fncode=" + appStore.config.deffncode;
  }
  request
    .get(baseurl)
    .then((response) => {
      if (response.data.code == 200) {
        // 将导航写入状态
        const navjson = response.data.data;
        isloading.value = false; //关闭等待页面
        localStorage.setItem("navjson", JSON.stringify(navjson));
        permissionStore.setNavData(navjson);
        router.push({ path: "/" });
      }
    })
    .catch((error) => {});
};
</script>
