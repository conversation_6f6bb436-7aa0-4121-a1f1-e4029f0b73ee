<template>
  <div class="warp">
    <!-- 后端：登录，返回token（只有用户信息）-
         前端：根据token获取租户列表， 选择租户+保存用户信息+获取菜单+跳转页面（通过login组件实现） -->
      <mylogin ref="mylogin" v-show="showLogin"></mylogin>
  </div>
</template>
<script>
import request from "@/utils/request";
import mylogin from "./index.vue";
import { getToken, setToken, removeToken } from "@/utils/auth";
export default {
  components:{mylogin},
  data() {
    return {
      value: "",
      value1: "",
      zhi: "",
      isloading: true,
      info: "成功",
      showLogin:false
    };
  },
  created() {},
  mounted() {
    this.Bindata();
  },
  methods: {
    Bindata() {
      this.value = window.location.href ? window.location.href : "";
      // this.value1 = this.value.split("?key=")[1];
      // console.log( this.value, this.value1)
      var temp=this.value.split('?')[1];
      var pram=new URLSearchParams('?'+temp);
      var Key=pram.get('key');
      var Tid=pram.get('tid');
      request
        .get("/auth/getLoginUser?key=" + Key)
        .then((res) => {
          if (res.data.code == 200) {
            this.showLogin=true;
            var getInfo = res.data.data.loginuser;
            localStorage.setItem("getInfo", JSON.stringify(getInfo));
            this.$store.state.user.userinfo = getInfo; //存储用户信息
            var token = res.data.data.access_token;
            setToken(token);
            this.$store.state.user.token = token; //存储Token
            console.log(getInfo.tenantid,'Tid',Tid)
            // 判断是否有租户传过来,无Tid-表示token中不包含租户信息，需要选择组合；
            // 有Tid-表示token返回数据中有租户信息，直接获取菜单跳转
            if(!getInfo.tenantid){  
            this.$refs.mylogin.getTenant(); // 选择租户
            }else{
              
            this.readnav();  // 获取菜单
            }
          } else {
            this.$confirm("获取用户信息失败，是否返回登录？", "提示", {
              confirmButtonText: "返回登录",
              cancelButtonText: "退出系统",
              type: "warning",
              closeOnClickModal: false,
            })
              .then((res) => {
                if (res === "confirm") {
                  // removeToken();
                  this.$router.push("/login");
                  this.$store.dispatch("user/logout");
                } else {
                  window.location.href = "about:blank";
                  window.close();
                }
              })
              .catch(() => {
                window.location.href = "about:blank";
                window.close();
              });
          }
        })
        .catch((err) => {
          this.$confirm("获取用户信息失败，是否返回登录？", "提示", {
            confirmButtonText: "返回登录",
            cancelButtonText: "退出系统",
            type: "warning",
            closeOnClickModal: false,
          })
            .then((res) => {
              console.log(res);
              if (res === "confirm") {
                removeToken();
                this.$store.dispatch("user/logout");
                this.$router.push("/login");
                
              } else {
                //   window.location.href="about:blank";
                window.close();
              }
            })
            .catch(() => {
              //   window.location.href="about:blank";
              window.close();
            });
        });
    },
    readnav() {
      request
        .get("/system/SYSM02B2/getMenuWebListBySelf").then((response) => {
          if (response.data.code == 200) {
            // 将导航写入状态
            var navjson = response.data.data;
            this.isloading = false; //关闭等待页面
            localStorage.setItem("navjson", JSON.stringify(navjson));
            this.$store.dispatch("app/setnavdata", navjson);
            // this.$router.push({ path: this.redirect || "/" });
              this.showWorkbench(); //工作台
          } else {
            this.$confirm("获取服务菜单失败，是否重试？", "提示", {
              confirmButtonText: "重试",
              cancelButtonText: "退出系统",
              type: "warning",
              closeOnClickModal: false,
            })
              .then((res) => {
                if (res === "confirm") {
                  this.readnav();
                } else {
                  window.close();
                }
              })
              .catch(() => {
                window.close();
              });
          }
        })
        .catch((error) => {});
    },
       // 工作台
    showWorkbench() {
      // console.log('this', this.$store.state.user)
      var data=this.$store.state.user.userinfo.configs['system.style.dashboard']
      if(data==null||data == ""){
        this.$router.push({ path: this.redirect || "/" });
      }else{
         var objdata = JSON.parse(data);
          this.$router.push(objdata.value);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.warp {
  position: relative;
  height: 100%;
}
</style>