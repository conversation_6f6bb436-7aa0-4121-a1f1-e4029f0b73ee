<template>
  <div class="warp">
    <!-- 后端：登录，返回token（只有用户信息）-
         前端：根据token获取租户列表， 选择租户+保存用户信息+获取菜单+跳转页面（通过login组件实现） -->
    <mylogin v-show="showLogin" ref="myloginRef"></mylogin>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { useUserStore, usePermissionStore } from "@/store";
import request from "@/utils/request";
import mylogin from "./index.vue";
import { setAccessToken, setRtKey, clearToken } from "@/utils/auth";

// Stores
const userStore = useUserStore();
const permissionStore = usePermissionStore();
const router = useRouter();

// Template refs
const myloginRef = ref();

// Reactive data
const value = ref("");
const value1 = ref("");
const zhi = ref("");
const isloading = ref(true);
const info = ref("成功");
const showLogin = ref(false);

// Lifecycle
onMounted(() => {
  Bindata();
});
// Methods
const Bindata = () => {
  value.value = window.location.href ? window.location.href : "";
  // value1.value = value.value.split("?key=")[1];
  // console.log( value.value, value1.value)
  const temp = value.value.split("?")[1];
  const pram = new URLSearchParams("?" + temp);
  const Key = pram.get("key");
  const Tid = pram.get("tid");

  request
    .get("/auth/getLoginUser?key=" + Key)
    .then((res) => {
      if (res.data.code == 200) {
        showLogin.value = true;
        const getInfo = res.data.data.loginuser;
        localStorage.setItem("getInfo", JSON.stringify(getInfo));
        userStore.setUserInfo(getInfo); // 存储用户信息
        const token = res.data.data.access_token;
        setAccessToken(token);
        setRtKey(res.data.data.rtkey);

        request
          .get(`/system/SYSM06B1/getConfigValue?key=platform.theme.mastercolor&tid=default`)
          .then((res) => {
            if (res.data.code === 200) {
              let masterColor = "#366cb3";
              if (res.data.data) {
                masterColor = res.data.data;
              }
              localStorage.setItem("inks-master-color", masterColor);
              const el = document.documentElement;
              el.style.setProperty("--inks-master-color", masterColor);
            }
          });

        console.log(getInfo.tenantid, "Tid", Tid);
        // 判断是否有租户传过来,无Tid-表示token中不包含租户信息，需要选择组合；
        // 有Tid-表示token返回数据中有租户信息，直接获取菜单跳转
        if (!getInfo.tenantid) {
          myloginRef.value?.getTenant(); // 选择租户
        } else {
          readnav(); // 获取菜单
        }
      } else {
        ElMessageBox.confirm("获取用户信息失败，是否返回登录？", "提示", {
          confirmButtonText: "返回登录",
          cancelButtonText: "退出系统",
          type: "warning",
          closeOnClickModal: false,
        })
          .then((res) => {
            if (res === "confirm") {
              clearToken();
              router.push("/login");
              userStore.logout();
            } else {
              window.location.href = "about:blank";
              window.close();
            }
          })
          .catch(() => {
            window.location.href = "about:blank";
            window.close();
          });
      }
    })
    .catch((err) => {
      ElMessageBox.confirm("获取用户信息失败，是否返回登录？", "提示", {
        confirmButtonText: "返回登录",
        cancelButtonText: "退出系统",
        type: "warning",
        closeOnClickModal: false,
      })
        .then((res) => {
          console.log(res);
          if (res === "confirm") {
            clearToken();
            userStore.logout();
            router.push("/login");
          } else {
            //   window.location.href="about:blank";
            window.close();
          }
        })
        .catch(() => {
          //   window.location.href="about:blank";
          window.close();
        });
    });
};
const readnav = () => {
  request
    .get("/system/SYSM02B2/getMenuWebListBySelf")
    .then((response) => {
      if (response.data.code == 200) {
        // 将导航写入状态
        const navjson = response.data.data;
        isloading.value = false; //关闭等待页面
        localStorage.setItem("navjson", JSON.stringify(navjson));
        // 使用 permissionStore 来设置导航数据
        permissionStore.setNavData(navjson);
        // router.push({ path: redirect || "/" });
        showWorkbench(); //工作台
      } else {
        ElMessageBox.confirm("获取服务菜单失败，是否重试？", "提示", {
          confirmButtonText: "重试",
          cancelButtonText: "退出系统",
          type: "warning",
          closeOnClickModal: false,
        })
          .then((res) => {
            if (res === "confirm") {
              readnav();
            } else {
              window.close();
            }
          })
          .catch(() => {
            window.close();
          });
      }
    })
    .catch((error) => {});
};

// 工作台
const showWorkbench = () => {
  // console.log('userStore', userStore.userInfo)
  const data = userStore.userInfo.configs?.["system.style.dashboard"];
  if (data == null || data == "") {
    router.push({ path: "/" });
  } else {
    const objdata = JSON.parse(data);
    router.push(objdata.value);
  }
};
</script>
<style lang="scss" scoped>
.warp {
  position: relative;
  height: 100%;
}
</style>
