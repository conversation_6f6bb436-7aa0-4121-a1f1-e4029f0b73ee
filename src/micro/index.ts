import { createApp, ref } from "vue";
import Dialog from "./subMicroDialog.vue";
import List from "./subMicroList.vue";
import Select from "./subMicroSelect.vue";
import Cite from "./subMicroCite.vue";
import Tab from "./subMicroTab.vue";
import router from "@/router";
import { useAppStore, useAppStoreHook, useMicroStore, useTagsViewStore } from "@/store";
import Cookies from "js-cookie";
import { ElLoading } from "element-plus";
import WujieVue from "wujie-vue3";
import JSZip from "jszip";
import { saveAs } from "file-saver";
import { urlMap } from "@/micro/register";
import request from "@/utils/request";
import enLocale from "@/lang/package/en";
import zhCnLocale from "@/lang/package/zh-cn";
// 声明 __WUJIE 类型
declare global {
  interface Window {
    __WUJIE: {
      id: string;
      [key: string]: any;
    };
    __GLOBAL_MODAL__: any;
    $wujie: any;
  }
}

let loadingInstance: any = null;
let microRoutesMap = {};
let microRoutesList: any[] = [];
let microState = ref({});
const routerSet = new Set();

function fetch(url: string, options: any) {
  return window.fetch(url, { ...options, credentials: "omit" });
}

function closeMicroLoading() {
  if (loadingInstance) {
    loadingInstance.close();
    loadingInstance.$el.parentElement.remove();
    loadingInstance = null;
  }
}

const lifecycles = {
  beforeLoad: (appWindow: Window) => {
    console.log(`${appWindow.__WUJIE.id} beforeLoad 生命周期`);
  },
  beforeMount: (appWindow: Window) => {
    console.log(`${appWindow.__WUJIE.id} beforeMount 生命周期`);
  },
  afterMount: (appWindow: Window) => {
    closeMicroLoading();
    console.log(`${appWindow.__WUJIE.id} afterMount 生命周期`);
  },
  beforeUnmount: (appWindow: Window) => {
    closeMicroLoading();
    console.log(`${appWindow.__WUJIE.id} beforeUnmount 生命周期`);
  },
  afterUnmount: (appWindow: Window) => console.log(`${appWindow.__WUJIE.id} afterUnmount 生命周期`),
  activated: (appWindow: Window) => {
    console.log(`${appWindow.__WUJIE.id} activated 生命周期`);
  },
  deactivated: (appWindow: Window) => {
    closeMicroLoading();
    console.log(`${appWindow.__WUJIE.id} deactivated 生命周期`);
  },
  loadError: (url: string, e: Error) => {
    closeMicroLoading();
    const key = new URL(url).searchParams.get("microping");
    if (key) {
      microState.value[key].state = 2;
    }
    console.log(`${url} 加载失败`, e);
  },
};

const plugins = [
  {
    jsBeforeLoaders: [
      {
        content: `
                window.addEventListener = (...params) => {window?.$wujie?.shadowRoot.addEventListener(...params)};
                window.removeEventListener = (...params) => {window?.$wujie?.shadowRoot.removeEventListener(...params)};
                document.addEventListener = (...params) => {window?.$wujie?.shadowRoot.firstChild.addEventListener(...params)};
                document.removeEventListener = (...params) => {window?.$wujie?.shadowRoot.firstChild.removeEventListener(...params)};
                `,
      },
      {
        callback(appWindow: Window) {
          closeMicroLoading();
          const props = appWindow.$wujie.props;
          const vWindow = appWindow.$wujie.props.vWindow;
          console.log("注入 loading", props);
          let container = document.querySelector(".app-main");
          let loadingCon = document.createElement("div");
          if (vWindow) {
            const openType = vWindow.params?.openType;
            const pageType = vWindow.params?.pageType;
            // edit select cite
            if (openType === 0 && [1, 2, 3].includes(pageType)) {
              const dialog = document.querySelector("body > div.wujie-page-body :last-child");
              container = dialog?.querySelector(".el-dialog__body");
            }
          }
          loadingCon.style.width = "100%";
          loadingCon.style.height = "100%";
          loadingCon.style.position = "absolute";
          loadingCon.style.top = "0";
          loadingCon.style.left = "0";
          container?.appendChild(loadingCon);
          let text = "应用加载中";

          if (props.pingName) {
            text = `${props.pingName}应用环境检测中`;
          }

          loadingInstance = ElLoading.service({
            text: text,
            target: loadingCon,
          });
        },
      },
    ],
  },
  {
    cssAfterLoaders: [
      // 修复列表页高级筛选下拉框偏移问题
      {
        content:
          "body { margin: 0 !important; padding: 0 !important; }" +
          "body > .el-popper{ position: absolute !important; }",
      },
    ],
  },
];

const getKeyData = (key: string, target?: string) => {
  const value = localStorage.getItem(key);
  let parseV = value;
  if (value !== null) {
    try {
      parseV = JSON.parse(value);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (err) {
      parseV = value;
    }
  }
  if (typeof parseV === "object" && parseV && target) {
    return parseV[target];
  }
  return parseV;
};

const proxyRouter = () => {
  return {
    push: (args: any) => {
      router.push(args);
    },
  };
};

const proxyCookie = () => {
  return {
    get: (name: string, value: any, options?: any) => Cookies.get(name, value, options),
    set: (name: string, options?: any) => Cookies.set(name, options),
    remove: (name: string, options?: any) => Cookies.remove(name, options),
  };
};

const setDocumentTitle = (title: string) => {
  const tagsViewStore = useTagsViewStore();
  document.title = `${title} - 订单管理系统`;
  tagsViewStore.updateVisitedView({
    title,
    path: window.location.pathname,
  });
};

const opensub = (value: Record<string, any>) => {
  WujieVue.bus.$emit("openSub", value);
};

const convertLangToList = (code) => {
  const en = enLocale[code] || {};
  const zh = zhCnLocale[code] || {};
  const enJson = Object.keys(en).map((key) => ({
    key: key,
    value: en[key],
  }));
  const zhJson = Object.keys(zh).map((key) => ({
    key: key,
    value: en[key],
  }));
  return {
    enJson,
    zhJson,
  };
};

const convertLangToMap = (list: { key: string; value: string }[]) => {
  const map = {};
  list.forEach((item) => {
    map[item.key] = item.value;
  });
  return map;
};
const wujieProps = {
  setDocumentTitle,
  getMainRouter: () => proxyRouter(),
  getMainCookie: () => proxyCookie(),
  getUserInfo: () => getKeyData("inks-userinfo"),
  getAppConfig: () => getKeyData("inks-appconfig"),
  getTenantinfo: () => getKeyData("inks-userinfo", "tenantinfo"),
  getRefreshToken: () => getKeyData("refresh_token"),
  getLanguage: () => {
    const appStore = useAppStore();
    if (appStore.config.i18n) {
      return appStore.language;
    }
    return "";
  },
  saveAs: saveAs,
  JSZip,
  opensub,
  convertLangToList,
  convertLangToMap,
  elMessage: ElMessage,
};

const PageWH = {
  "0": ["100%", "100%"],
  "1": ["90vw", "calc(100vh - 60px)"],
  "2": ["40vw", "45vh"],
  "3": ["85vw", "calc(100vh - 40px)"],
};

const components = {
  0: List,
  1: Dialog,
  2: Select,
  3: Cite,
};

WujieVue.bus.$on("openSub", (value: Record<string, any>) => {
  const { params, page } = value;
  if (!page.microMode) {
    console.warn("v-window: 非微应用模式,不支持打开子应用");
    return;
  }
  const microUrl = `${page.microEntry}`;
  const { openType, pageType, lstp, bindData, microcallback } = params;

  const props = {
    ...wujieProps,
    lstp: lstp,
    vWindow: value,
  };

  let wh = PageWH[params.pageType];

  if (microcallback && openType === 0) {
    props.microcallback = microcallback;
  }
  if (bindData && openType === 0) {
    props.bindData = bindData;
  }

  let microName = microUrl;

  const subMicroDetail = {
    microProps: props,
    microUrl: microUrl,
    microName: microName,
    isMicro: true,
    sync: false,
    width: wh[0],
    height: wh[1],
    title: page.title || "",
  };

  if (!urlMap.has(microUrl)) {
    WujieVue.setupApp({
      name: microName,
      url: microUrl,
      exec: true,
      fetch: fetch,
      attrs: {},
      ...lifecycles,
      plugins: plugins,
      props: props,
    });
    urlMap.set(microUrl, true);
    console.log(`v-window: 子应用地址手动注册成功 ${microUrl}`);
  }
  // tabs新页面功能
  if (openType === 1) {
    // 存储lstp参数
    const microStore = useMicroStore();
    let mdRouteName = page.routePath;
    let mdRoutePath = page.routePath;
    // 第一个首位的字符带了/ 需要去掉
    if (mdRoutePath.at(0) === "/") {
      mdRouteName = mdRoutePath.substring(1);
    }

    microStore.cacheMicroItem(mdRouteName, subMicroDetail);
    console.log("铆钉页面数据:", microStore.getMicroItem(mdRouteName));
    if (routerSet.has(mdRouteName)) {
      routerSet.delete(mdRouteName);
      if (router.hasRoute(mdRouteName)) {
        router.removeRoute(mdRouteName);
      }
    }
    // 新增路由
    if (!routerSet.has(mdRouteName)) {
      router.addRoute("/", {
        path: mdRouteName,
        name: mdRouteName,
        component: Tab,
        props: subMicroDetail,
        meta: { title: page.title || "", hidden: true, keepAlive: true, ismicroapp: 1 },
      });
      routerSet.add(mdRouteName);
    }
    console.log("铆钉跳转至: ", mdRoutePath);
    router.push({ path: mdRoutePath });
    return;
  }

  let instance: any;
  let mountNode: HTMLDivElement | null = document.createElement("div");
  mountNode.className = "wujie-page-body";
  subMicroDetail.microProps.close = () => {
    if (instance) {
      instance.unmount();
      instance = null;
    }
    if (mountNode) {
      mountNode.remove();
      mountNode = null;
    }

    WujieVue.destroyApp(microName);
    urlMap.delete(microUrl);
  };
  let component = components[pageType] || Dialog;
  instance = createApp(component, subMicroDetail);
  if (pageType === 0) {
    instance.use(router);
    document.querySelector(".app-main")?.appendChild(mountNode);
  } else if (pageType !== 0) {
    document.body.appendChild(mountNode);
  }
  instance.mount(mountNode);
});

let preTime = new Date().getTime();

const checkProxyMicro = (check) => {
  request.get("/system/SYSM06B1/getMicroAppMapList").then((res) => {
    if (res.data.code === 200) {
      const navconfig = {};
      res.data.data.forEach((item) => {
        navconfig[item.key] = item.value;
      });
      let microKeys: any[] = Object.keys(navconfig);
      const localnet = getKeyData("inks-appconfig")?.localnet;
      microKeys.forEach((key) => {
        let microurl = navconfig[key];
        if (localnet && [1, "1"].includes(localnet)) {
          microurl = microurl.split(";")[0];
        } else {
          microurl = microurl.split(";")[1];
        }
        microurl += `/${key}/`;
        microState.value[key] = {
          key: key,
          url: microurl,
          state: check ? 3 : 0, // 0未知  1:正常  2:异常  3: 检测中
        };
      });

      microKeys.forEach((key) => {
        let microurl = navconfig[key];
        if (localnet && [1, "1"].includes(localnet)) {
          microurl = microurl.split(";")[0];
        } else {
          microurl = microurl.split(";")[1];
        }
        microurl += `/${key}/?microping=${key}`;
        const uname = `${key}_状态检测_${preTime}`;
        WujieVue.destroyApp(uname);
        WujieVue.setupApp({
          name: uname,
          url: microurl,
          exec: true,
          fetch: fetch,
          attrs: {},
          ...lifecycles,
          plugins: plugins,
          props: {
            ...wujieProps,
            initMicro: true,
            ping: true,
            pingName: key,
          },
        });
        WujieVue.preloadApp({ name: uname });

        let origin = microurl;
        if (microurl[microurl.length - 1] == "/") {
          origin = microurl.slice(0, -1);
        }
        WujieVue.bus.$once(`${key}:routes`, (value: any) => {
          console.log(`${key}:routes`, value);
          value.routes.forEach((item) => {
            item.fullpath = `${origin}${item.path}`;
          });
          microRoutesMap[value.form] = value.routes;
          microRoutesList.push(...value.routes);
        });
      });

      preTime = new Date().getTime();
    }
  });
};

WujieVue.bus.$on("pong", (value: any) => {
  const { key, state } = value;
  if (microState.value[key]) {
    microState.value[key].state = state;
    console.log("microState", microState);
  }
});

const getMicroState = () => {
  return microState;
};

const microClear = () => {
  const tagsViewStore = useTagsViewStore();
  tagsViewStore.delAllViews();
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key) {
      if (!["inks-subapp", "inks-appconfig"].includes(key)) {
        localStorage.removeItem(key);
      }
    }
  }
  sessionStorage.clear();
  const cookies = Cookies.get();
  for (const cookieName in cookies) {
    Cookies.remove(cookieName);
  }
};

const updateMicroLanguage = () => {
  const appStore = useAppStoreHook();
  WujieVue.bus.$emit("updateLanguage", appStore.language);
};

// const bindEventListener = function (type: string) {
//   const historyEvent = history[type];
//   return function () {
//     const newEvent = historyEvent.apply(this, arguments);
//     const e: Event = new Event(type);
//     e.arguments = arguments;
//     window.dispatchEvent(e);
//     return newEvent;
//   };
// };
// history.pushState = bindEventListener("pushState");
// history.replaceState = bindEventListener("replaceState");
// function anchorElementGenerator(url: string) {
//   const element = window.document.createElement("a");
//   element.href = url;
//   // element.href = element.href; // hack ie
//   return element;
// }
// function getAnchorElementQueryMap(anchorElement: any) {
//   const queryString = anchorElement.search || "";
//   return [...new URLSearchParams(queryString).entries()].reduce((p: any, c) => {
//     p[c[0]] = c[1];
//     return p;
//   }, {});
// }
// window.addEventListener("replaceState", function () {
//   let winUrlElement = anchorElementGenerator(window.location.origin + window.location.pathname);
//   const queryMap = getAnchorElementQueryMap(winUrlElement);
//   winUrlElement.search =
//     "?" +
//     Object.keys(queryMap)
//       .map((key) => key + "=" + window.decodeURIComponent(queryMap[key]))
//       .join("&");
//   if (winUrlElement.href !== window.location.href) {
//     console.warn("主应用sync同步replaceState");
//     window.history.replaceState(null, "", winUrlElement.href);
//   }
// });

export {
  fetch,
  lifecycles,
  getKeyData,
  proxyRouter,
  proxyCookie,
  setDocumentTitle,
  closeMicroLoading,
  wujieProps,
  plugins,
  microState,
  checkProxyMicro,
  getMicroState,
  microClear,
  updateMicroLanguage,
};
