<template>
  <div ref="wujieList" class="wujie-dialog-list">
    <wujie-vue
      width="100%"
      height="100%"
      :url="microUrl"
      :name="microName"
      :props="microProps"
      :plugins="plugins"
      :alive="false"
      :sync="sync"
      :beforeLoad="lifecycles.beforeLoad"
      :beforeMount="lifecycles.beforeMount"
      :afterMount="lifecycles.afterMount"
      :beforeUnmount="lifecycles.beforeUnmount"
      :afterUnmount="lifecycles.afterUnmount"
      :activated="lifecycles.activated"
      :deactivated="lifecycles.deactivated"
      :loadError="lifecycles.loadError"
    />
  </div>
</template>
<script setup lang="ts">
import { defineProps } from "vue";
import { useRoute } from "vue-router";
import WujieVue from "wujie-vue3";
import { lifecycles, plugins } from "@/micro/index";
defineProps({
  microProps: {
    type: Object,
    required: true,
  },
  microUrl: {
    type: String,
    required: true,
  },
  microName: {
    type: String,
    required: true,
  },
  isMicro: {
    type: Boolean,
    required: true,
  },
  sync: {
    type: Boolean,
    required: true,
  },
  height: {
    type: String,
    required: false,
  },
  width: {
    type: String,
    required: false,
  },
});
const route = useRoute();
const wujieList = ref(null);
const curr = ref("");
onMounted(() => {
  curr.value = route.fullPath;
});
// 监听route
watch(
  () => route,
  () => {
    nextTick(() => {
      if (curr.value !== route.fullPath) {
        // 配置display none
        wujieList.value.style.background = "rgba(255,255,255,1)";
        const child = wujieList.value.querySelector("div");
        child.style.display = "none";
        setTimeout(() => {
          wujieList.value.style.display = "none";
          wujieList.value.style.background = "rgba(255,255,255,0)";
        });
      } else {
        wujieList.value.style.display = "block";
        const child = wujieList.value.querySelector("div");
        child.style.display = "block";
      }
    });
  },
  { deep: true }
);
</script>

<style scoped lang="scss">
.wujie-dialog-list {
  position: absolute;
  background: #fff;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: 100%;
}
</style>
