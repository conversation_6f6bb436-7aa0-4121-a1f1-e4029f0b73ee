<template>
  <div class="wujie-dialog-list">
    <wujie-vue
      width="100%"
      height="100%"
      :url="microUrl"
      :name="microName"
      :props="microProps"
      :plugins="plugins"
      :alive="true"
      :sync="sync"
      :beforeLoad="lifecycles.beforeLoad"
      :beforeMount="lifecycles.beforeMount"
      :afterMount="lifecycles.afterMount"
      :beforeUnmount="lifecycles.beforeUnmount"
      :afterUnmount="lifecycles.afterUnmount"
      :activated="lifecycles.activated"
      :deactivated="lifecycles.deactivated"
      :loadError="lifecycles.loadError"
    />
  </div>
</template>
<script setup lang="ts">
import { defineProps } from "vue";
import WujieVue from "wujie-vue3";
import { lifecycles, plugins } from "@/micro/index";
defineProps({
  microProps: {
    type: Object,
    required: true,
  },
  microUrl: {
    type: String,
    required: true,
  },
  microName: {
    type: String,
    required: true,
  },
  isMicro: {
    type: Boolean,
    required: true,
  },
  sync: {
    type: Boolean,
    required: true,
  },
  height: {
    type: String,
    required: false,
  },
  width: {
    type: String,
    required: false,
  },
});
</script>

<style scoped lang="scss">
.wujie-dialog-list {
  position: absolute;
  background: #fff;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: 100%;
}
</style>
