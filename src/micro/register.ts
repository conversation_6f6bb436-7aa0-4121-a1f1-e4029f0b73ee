import router, { unFoundRoutes, adminRoute } from "@/router";
import WujieVue from "wujie-vue3";
import { fetch, lifecycles, wujieProps, plugins } from "@/micro/index";
import manualMicro from "@/micro/manualMicro.json";

const urlKeyMap = new Map();
const urlMap = new Map();
const microSet = new Set();

export interface IMicroRoute {
  ismicroapp: number;
  microappentry: string;
  microappname: string;
  microapprule: string;
  microurl?: string;
}

/**
 * 注册微应用
 * @param microRoute
 */
function setupMicroApps(microRoute: IMicroRoute[]) {
  const UrlSet = new Set();
  urlMap.clear();
  urlKeyMap.clear();
  microSet.clear();
  microRoute.forEach((route) => {
    const { microappname, microurl, microappentry, microapprule, title } = route;
    UrlSet.add(microappentry);
    const prefix = microapprule.split("/")[1];
    if (!urlKeyMap.has(prefix)) {
      urlKeyMap.set(prefix, [microappentry]);
    } else {
      const m = urlKeyMap.get(prefix);
      if (!m.includes(microappentry)) {
        m.push(microappentry);
      }
    }
    urlMap.set(microurl, title);
    const props = { ...wujieProps };
    if (!props.vWindow) {
      props.vWindow = {
        page: {
          microMode: true,
          microEntry: microurl,
          pageCode: microappname,
        },
      };
    }
    WujieVue.setupApp({
      name: microappname,
      url: microurl,
      exec: true,
      fetch: fetch,
      attrs: {},
      ...lifecycles,
      plugins: plugins,
      props: props,
    });
    microSet.add(microappname);
  });
  console.log("子应用地址信息", Object.fromEntries(urlMap));
}

function destroyMicroApps() {
  const sandboxMap = window.__WUJIE_INJECT.idToSandboxMap || new Map();
  sandboxMap.forEach((v, key) => {
    WujieVue.destroyApp(key);
  });
}

/**
 * 缓存用户常用的微应用数据
 * @param route
 */
function cacheMicroApps(route) {
  const { ismicroapp, microappname } = route.meta;
  const name = route.name;
  if (ismicroapp === 1 && microappname) {
    let subApp = localStorage.getItem("inks-subapp");
    if (subApp) {
      subApp = JSON.parse(subApp) || [];
      const microApp = subApp.find((item) => item.micro === microappname);
      if (!microApp) {
        subApp.push({
          name,
          micro: microappname,
          count: 1,
        });
      } else {
        microApp.count++;
      }
      localStorage.setItem("inks-subapp", JSON.stringify(subApp));
    } else {
      localStorage.setItem(
        "inks-subapp",
        JSON.stringify([
          {
            name,
            micro: microappname,
            count: 1,
          },
        ])
      );
    }
  }
}

/**
 * 预加载微应用
 */
function preloadMicroApp(preloadNum = 5) {
  let subApp = localStorage.getItem("inks-subapp");
  const load = (info) => {
    if (microSet.has(info.micro)) {
      console.log("预加载子应用:", info.name);
      WujieVue.preloadApp({ name: info.micro });
    }
  };
  if (subApp) {
    subApp = JSON.parse(subApp) || [];
    // 排序 count
    subApp.sort((a, b) => b.count - a.count);
    if (subApp.length === 1) {
      load(subApp[0]);
      return;
    }
    for (let i = 0; i < subApp.length - 1; i++) {
      if (i > preloadNum - 1) {
        break;
      }
      load(subApp[i]);
    }
  }
}

/**
 * 注册401/404等路由
 */
function generateUnFoundRoutes() {
  unFoundRoutes.forEach((route) => {
    router.addRoute("/", route);
  });
}

/**
 * 注册管理员路由
 */
function generateAdminRoutes() {
  let userInfoData;
  const inksUserInfoData = localStorage.getItem("inks-userinfo");
  if (inksUserInfoData) {
    userInfoData = JSON.parse(inksUserInfoData) || "";
  }
  if (userInfoData && userInfoData.isadmin === 1) {
    adminRoute.forEach((route) => {
      router.addRoute("/", route);
    });
  }
}

function manualRegisterMicro() {
  let appconfig = localStorage.getItem("inks-appconfig");
  appconfig = JSON.parse(appconfig) || {};
  const model = appconfig.model;
  const manual = manualMicro.filter((item) => item.model.includes(model));
  console.log("manualMicro", manual);
  manual.forEach((item) => {
    const props = { ...wujieProps };
    const microappurl = `${item.microappentry}${item.microapprule}`;
    item.microappurl = microappurl;
    if (!props.vWindow) {
      props.vWindow = {
        page: {
          microMode: true,
          microEntry: item.microappurl,
          pageCode: item.microappname,
        },
      };
    }
    router.addRoute("/", {
      path: item.routerPath,
      name: item.microappname,
      component: WujieVue,
      props: {
        url: item.microappurl,
        name: item.microappname,
        alive: true,
      },
      meta: {
        title: item.microapplabel,
        hidden: true,
        keepAlive: true,
        ismicroapp: 1,
        microappentry: item.microappurl,
        microappname: item.microappname,
        microapprule: item.routerPath,
        microurl: item.microappurl,
      },
    });
    WujieVue.setupApp({
      name: item.microappname,
      url: item.microappurl,
      exec: true,
      fetch: fetch,
      attrs: {},
      ...lifecycles,
      plugins: plugins,
      props: props,
    });
  });
}
export {
  setupMicroApps,
  generateUnFoundRoutes,
  generateAdminRoutes,
  manualRegisterMicro,
  destroyMicroApps,
  cacheMicroApps,
  preloadMicroApp,
  urlMap,
  urlKeyMap,
};
