<template>
  <div class="wujie-dialog">
    <el-dialog
      :model-value="true"
      align-center
      destroy-on-close
      :title="title || ''"
      :close-on-click-modal="false"
      :width="width ? width : '90vw'"
      :show-close="true"
      :close-on-press-escape="true"
      :style="{
        height: height ? height : 'calc(100vh - 60px)',
        display: 'flex',
        flexDirection: 'column',
      }"
    >
      <div v-if="isMicro" class="wujie-dialog-container">
        <wujie-vue
          width="100%"
          height="100%"
          :url="microUrl"
          :name="microName"
          :props="microProps"
          :plugins="plugins"
          :alive="false"
          :sync="sync"
          :beforeLoad="lifecycles.beforeLoad"
          :beforeMount="lifecycles.beforeMount"
          :afterMount="lifecycles.afterMount"
          :beforeUnmount="lifecycles.beforeUnmount"
          :afterUnmount="lifecycles.afterUnmount"
          :activated="lifecycles.activated"
          :deactivated="lifecycles.deactivated"
          :loadError="lifecycles.loadError"
        />
      </div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { defineProps } from "vue";
import WujieVue from "wujie-vue3";
import { ElDialog } from "element-plus";
import { lifecycles, plugins } from "@/micro/index";
defineProps({
  microProps: {
    type: Object,
    required: true,
  },
  microUrl: {
    type: String,
    required: true,
  },
  microName: {
    type: String,
    required: true,
  },
  isMicro: {
    type: Boolean,
    required: true,
  },
  sync: {
    type: Boolean,
    required: true,
  },
  height: {
    type: String,
    required: false,
  },
  width: {
    type: String,
    required: false,
  },
  title: {
    type: String,
    required: true,
  },
});
</script>

<style scoped lang="scss">
.wujie-dialog {
  position: relative;
  z-index: 9999;
  :deep(.el-overlay-dialog) {
    overflow: hidden !important;
    .el-dialog {
      .el-dialog__header {
        padding: 0;
      }
      .el-dialog__body {
        flex: 1;
        overflow: auto;
      }
    }
  }
}
.wujie-dialog-container {
  transform: translateY(20px);
}
</style>
