<template>
  <div class="navbar">
    <div
      style="
        display: inlin-block;
        display: flex;
        flex: 1;
        align-items: center;
        width: 100px;
        height: 50px;
      "
      class="site_name"
    >
      <img :src="logo" width="35px" alt="" style="margin-left: 12px" />
      <!-- 订单管理系统标准版dev -->
      <span style="margin-left: 20px; font-size: 18px; color: #4d4f51">
        {{ appStore.config.title }}
      </span>
    </div>
    <!-- 导航栏右侧 -->
    <NavbarRight />
  </div>
</template>
<script lang="ts">
export default {
  name: "NavBar",
};
</script>
<script setup lang="ts">
import { useAppStore } from "@/store";
const logo =
  "data:image/png;base64,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";

const appStore = useAppStore();
</script>

<style lang="scss" scoped>
.navbar {
  display: flex;
  justify-content: space-between;
  height: $navbar-height;
  background: var(--el-bg-color);

  &__left {
    display: flex;
    align-items: center;
  }
}
</style>
