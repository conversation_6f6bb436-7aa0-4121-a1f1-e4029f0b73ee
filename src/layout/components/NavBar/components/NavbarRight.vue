<template>
  <div :class="['navbar__right', navbarRightClass]">
    <div class="icon-list">
      <div id="early-warn" class="icon-item">
        <!-- 预警 -->
        <el-tooltip effect="dark" content="预警" placement="bottom">
          <el-icon :size="20">
            <BellFilled class="nav-icon not-allowed" />
          </el-icon>
        </el-tooltip>
      </div>
      <div class="icon-item">
        <!-- 帮助中心 -->
        <el-dropdown class="avatar-container" trigger="click" :hide-on-click="false">
          <el-icon :size="20">
            <QuestionFilled class="nav-icon" />
          </el-icon>
          <template #dropdown>
            <el-dropdown-menu slot="dropdown" class="user-dropdown">
              <router-link to="/search" target="_blank">
                <el-dropdown-item>帮助中心</el-dropdown-item>
              </router-link>
              <div @click="guideStart">
                <el-dropdown-item>新手引导</el-dropdown-item>
              </div>
              <el-dropdown-item>
                <el-popover placement="left-start" width="225" trigger="click">
                  <div style="text-align: center">
                    <span>打开微信扫一扫</span>
                    <div style="width: 200px; margin-top: 10px">
                      <img
                        src="@/assets/images/knows_images/gh_b387040dcb66_258.jpg"
                        alt=""
                        srcset=""
                        style="width: 100%; height: 100%; border: 1px solid #ddd"
                      />
                    </div>
                  </div>
                  <template #reference>
                    <div slot="reference">用户中心</div>
                  </template>
                </el-popover>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="icon-item">
        <!-- 刷新 -->
        <el-tooltip content="刷新" effect="dark" placement="bottom">
          <el-icon :size="20" @click="refreshBtn">
            <Refresh class="nav-icon" />
          </el-icon>
        </el-tooltip>
      </div>
      <div class="icon-item">
        <!-- 全屏 -->
        <el-icon :size="16">
          <Fullscreen class="nav-icon" />
        </el-icon>
      </div>
      <!-- 语言选择 -->
      <div v-if="appStore.config.i18n" class="icon-item">
        <LangSelect />
      </div>
    </div>

    <!-- 用户头像（个人中心、注销登录等） -->
    <el-dropdown trigger="click">
      <div class="user-profile">
        <img class="user-profile__avatar" :src="userStore.userInfo.avatar" />
        <span class="user-profile__name user-ellipsis">{{ userStore.userInfo.realname }}</span>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="handleProfileClick">个人中心</el-dropdown-item>
          <el-dropdown-item @click="router.push({ name: 'LoginLog' })">登录日志</el-dropdown-item>
          <el-dropdown-item @click="router.push({ name: 'SettingLog' })">操作日志</el-dropdown-item>
          <el-dropdown-item @click="openCountSuccessDialog">用户活跃</el-dropdown-item>
          <el-dropdown-item @click="openNewWebTab('https://pms.inksyun.com')">
            插件下载
          </el-dropdown-item>
          <el-dropdown-item @click="openNewWebTab('https://www.inkstech.com')">
            应凯管网
          </el-dropdown-item>
          <el-dropdown-item
            v-if="model === 'cluster'"
            divided
            @click="router.push({ name: 'SYSM02B1' })"
          >
            应用市场
          </el-dropdown-item>
          <el-dropdown-item
            v-if="userStore.userInfo.isadmin === 1"
            divided
            @click="router.push({ name: 'MicroState' })"
          >
            微应用管理
          </el-dropdown-item>
          <el-dropdown-item divided @click="logout">{{ t("system.logout") }}</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 设置面板 -->
    <div v-if="false" @click="settingStore.settingsVisible = true">
      <div class="i-svg:setting" />
    </div>
  </div>
  <el-dialog
    v-model="openCountSuccess"
    width="400px"
    :close-on-click-modal="true"
    class="downDialog"
    :modal="true"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="flex" style="align-items: center">
        <div style="margin-right: 8px">
          {{ scoptype ? "用户周活跃榜" : "用户月活跃榜" }}
        </div>
        <el-switch
          v-model="scoptype"
          style="--el-switch-off-color: #13ce66"
          inactive-color="#13ce66"
          @change="openCountSuccessDialog"
        ></el-switch>
      </div>
    </template>
    <div>
      <ul class="RankList">
        <li
          v-for="(item, index) in userSuccessList"
          :key="index"
          class="flex j-s a-c RankList-li"
          :style="getRankListStyle(item, index)"
        >
          <div>
            <span class="RankList-num" :style="getRankListNumStyle(item, index)">
              {{ index + 1 }}
            </span>
            <span>{{ item.realname }}</span>
          </div>
          <span>{{ item.success }}</span>
        </li>
      </ul>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import LangSelect from "@/components/LangSelect/index.vue";
import { ThemeMode } from "@/enums/settings/theme.enum";
import { useAppStore, useSettingsStore, useUserStore } from "@/store";
import guide from "../../../GUIDE.ts"; //新手指引
import Driver from "driver.js"; //  zhang 新手指引依赖
import "driver.js/dist/driver.min.css";
import request from "@/utils/request";
import { microClear } from "@/micro";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const settingStore = useSettingsStore();
const userStore = useUserStore();
const appStore = useAppStore();
// const route = useRoute();
const router = useRouter();

function refreshBtn() {
  window.location.reload();
}

/**
 * 打开个人中心页面
 */
function handleProfileClick() {
  router.push({ name: "Profile" });
}

// 根据主题和侧边栏配色方案选择 navbar 右侧的样式类
const navbarRightClass = computed(() => {
  // 如果暗黑主题
  if (settingStore.theme === ThemeMode.DARK) {
    return "navbar__right--white";
  }
});

/**
 * 注销登录
 */
function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    lockScroll: false,
  }).then(() => {
    userStore
      .logout()
      .then(() => {
        microClear();
      })
      .then(() => {
        window.location.href = "/login";
      });
  });
}

/**
 * 新手引导
 */
function guideStart() {
  const driver = new Driver({
    allowClose: false, //禁止点击外部关闭
    doneBtnText: "完成", // 完成按钮标题
    closeBtnText: "关闭", // 关闭按钮标题
    stageBackground: "#fff", // 引导对话的背景色
    nextBtnText: "下一步", // 下一步按钮标题
    prevBtnText: "上一步", // 上一步按钮标题
  });
  driver.defineSteps(guide);
  driver.start();
}

const userSuccessList = ref([]);
const scoptype = ref(false);
const openCountSuccess = ref(false);

function openNewWebTab(url) {
  window.open(url, "_blank");
}

function openCountSuccessDialog() {
  userSuccessList.value = [];
  request
    .get("/system/SYSM08B1/getCountSuccessByUser?scope=" + (scoptype.value ? 1 : 0))
    .then((res) => {
      if (res.data.code == 200) {
        userSuccessList.value = [...res.data.data];
        const length = userSuccessList.value.length;
        if (length < 5) {
          const num = 5 - length;
          for (let i = 0; i < num; i++) {
            userSuccessList.value.push({
              realname: "",
              success: "",
            });
          }
        }
        openCountSuccess.value = true;
      }
    });
}

function getRankListStyle(data, index) {
  let sty = { background: "#FFF" };
  if (index == 0) {
    sty = { background: "#fffed3" };
  } else if (index == 1) {
    sty = { background: "#d3f0ff" };
  } else if (index == 2) {
    sty = { background: "#ffd3d3" };
  }
  return sty;
}

function getRankListNumStyle(data, index) {
  let sty = { background: "#C0C0C0" };
  if (index == 0) {
    sty = { background: "#FFD700" };
  } else if (index == 1) {
    sty = { background: "#409eff" };
  } else if (index == 2) {
    sty = { background: "#B87333" };
  }
  return sty;
}

const model = ref("");
let appconfig = localStorage.getItem("inks-appconfig");
if (appconfig) {
  appconfig = JSON.parse(appconfig);
  model.value = appconfig.model;
}
</script>

<style lang="scss" scoped>
.navbar__right {
  display: flex;
  align-items: center;
  justify-content: center;

  & > * {
    display: inline-block;
    min-width: 40px;
    height: $navbar-height;
    line-height: $navbar-height;
    color: var(--el-text-color);
    text-align: center;
    cursor: pointer;

    &:hover {
      //background: rgb(0 0 0 / 10%);
    }
  }

  .user-profile {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 13px;

    &__avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }

    &__name {
      margin-left: 10px;
    }
  }
}

.layout-top .navbar__right > *,
.layout-mix .navbar__right > * {
  color: #fff;
}

.dark .navbar__right > *:hover {
  color: #ccc;
}

.user-ellipsis {
  max-width: 65px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.icon-list {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .icon-item {
    margin-left: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.nav-icon {
  color: rgb(96, 98, 102);

  &:hover {
    color: var(--el-color-primary);
  }
}

.not-allowed {
  cursor: not-allowed;
}

.RankList {
  padding: 0;
  margin: 0;
  font-size: 16px;
  color: #5a5e66;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;

  .RankList-li {
    border: 1px solid #ddd;
    padding: 15px 20px;
    border-radius: 8px;
    margin: 6px;
  }

  .RankList-num {
    margin-right: 10px;
    background: #9a9ca1;
    // border:1px solid #9a9ca1;
    color: #fff;
    border-radius: 50%;
    padding: 4px 8px;
  }
}
</style>
