<!--
 * @file SidebarMenuCustom.vue
 * @description 侧面菜单页面
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-24 caoshi 修改页面结构、inksoms改为appconfig读取、子菜单弹出位置修改
 * 2025-04-25 caoshi 加入getSidebarBridge、getSidebarLnk
 * 2025-04-26 caoshi getqMenuList、getAppliList改为如果浏览器中无数据 读取API
-->
<template>
  <!-- 【ding】新增外层 div start 注意下面有新增 custom-menu-container 样式 -->
  <div class="custom-menu-container">
    <div class="home-title">
      <router-link :to="dashboardPath">主页</router-link>
    </div>
    <div class="container">
      <ul class="container_ul">
        <!-- 一级菜单循环 -->
        <template v-for="menu in navData">
          <li class="menu-first" @click="routerTo(menu.path)">
            <div class="menu-first-item" @mousemove="handleMouseMove">
              <div class="iconStyle">
                <i
                  v-if="menu.meta.icon.includes('el-icon') && !!menu.meta.icon"
                  :class="menu.meta.icon"
                />
                <svg-icon
                  v-else-if="!!menu.meta.icon"
                  :icon-class="menu.meta.icon"
                  :style="{
                    'font-size': '24px',
                    color: '#f8f8f8',
                    margin: '8px auto 0px 2px',
                  }"
                />
                <i v-else-if="!menu.meta.icon" :class="'el-icon-location-outline'" />
              </div>
              <div>{{ menu.meta.title }}</div>
            </div>
            <div v-if="menu.children && menu.children.length > 0" class="mask">
              <div class="menu-second-wrap">
                <!-- 二级菜单循环 -->
                <template v-for="item in menu.children">
                  <ul>
                    <!-- 二级菜单大类 -->
                    <li class="menu-second-name">
                      {{ item.meta.title }}
                    </li>
                    <template v-for="secondMenu in item.children">
                      <!-- 二级菜单小类 -->
                      <li class="menu-second">
                        <router-link :to="secondMenu.path">
                          <div class="menu-scond-item">
                            <div class="iconStyle">
                              <i
                                v-if="
                                  secondMenu.meta.icon.includes('el-icon') && !!secondMenu.meta.icon
                                "
                                :class="secondMenu.meta.icon"
                              />
                              <svg-icon
                                v-else-if="!!secondMenu.meta.icon"
                                :icon-class="secondMenu.meta.icon"
                                :style="{
                                  'font-size': '24px',
                                  color: '#f8f8f8',
                                  margin: '8px auto 0px 2px',
                                }"
                              />
                              <i
                                v-else-if="!secondMenu.meta.icon"
                                :class="'el-icon-location-outline'"
                              />
                            </div>
                            <div class="menu-scond-item-name">{{ secondMenu.meta.title }}</div>
                          </div>
                        </router-link>
                      </li>
                    </template>
                  </ul>
                </template>
              </div>
            </div>
          </li>
        </template>
      </ul>

      <div class="control">
        <ul>
          <li @click="getqMenuList()">
            <el-icon color="#fff" size="25">
              <Menu />
            </el-icon>
            <div class="mask-b">
              <div class="login-list-wrap fast">
                <div class="title">
                  <span>快捷键</span>
                  <el-icon size="20" @click="openMenuDialog">
                    <Setting />
                  </el-icon>
                </div>
                <ul class="login-ul">
                  <li>
                    <!-- <div class="icon"></div>
                    <div class="login-name">快速开关</div> -->
                    <div class="qmenu-body">
                      <div
                        v-for="(o, index) in qMenuList"
                        :key="index"
                        class="qmenu-bodyItem"
                        @click="routerTo(o.link)"
                      >
                        <div
                          class="iconStyle"
                          :style="{ background: o.background ? o.background : '#ff9800' }"
                        >
                          <component
                            :is="o.vue3Icon"
                            v-if="o.icon.includes('el-icon') && !!o.icon"
                            style="color: #fff; width: 30px; height: 30px"
                          ></component>
                          <svg-icon
                            v-else-if="!!o.icon"
                            :icon-class="o.icon"
                            :style="{
                              'font-size': '24px',
                              color: '#f8f8f8',
                            }"
                          />
                          <i v-else-if="!o.icon" :class="'el-icon-location-outline'" />
                        </div>
                        <p class="qmenu-title">{{ o.label }}</p>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </li>

          <li>
            <svg-icon icon-class="rtimg" size="40px"></svg-icon>
            <div ref="appmask" class="mask-b">
              <div class="login-list-wrap">
                <div class="title">应用管理</div>
                <ul class="login-ul">
                  <li v-for="(o, index) in appliList" :key="index">
                    <!-- <div class="icon"></div> -->
                    <!-- <div class="login-name">ADN安灯系统</div> -->
                    <div class="bodyItem" @click="updateRouter(o)">
                      <img
                        v-if="!!o.frontphoto"
                        :src="'data:image/jpeg;base64,' + o.frontphoto"
                        class="iconStyle"
                      />
                      <div
                        v-else
                        class="iconStyle"
                        :style="{ background: o.background ? o.background : '#ff9800' }"
                      >
                        <svg-icon
                          :icon-class="'D01M01B2'"
                          :style="{
                            'font-size': '24px',
                            color: '#f8f8f8',
                          }"
                        />
                      </div>
                      <div class="bodyItem_title">
                        <div>{{ o.functionname }}</div>
                        <div>{{ o.description }}</div>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <el-dialog v-model="dialogVisible" title="快捷键操作" width="800">
      <div style="height: 600px">
        <TransferTemp
          :titles="['菜单栏', '已选菜单']"
          :data="allMenuList"
          :rightVal="qMenuList"
          @savetranser="savetranser"
        />
      </div>
      <template v-if="false" #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogVisible = false">添加</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
  <!-- 【ding】新增外层 div end -->
</template>

<script setup>
// import { menus } from '@/utils/menus'
// import { getRtKey } from "@/utils/auth";
import { useAppStore, usePermissionStore, useTagsViewStore } from "@/store";
import TransferTemp from "./TransferTemp/index.vue";
import router from "@/router";
import request from "@/utils/request";
import { destroyMicroApps, setupMicroApps } from "@/micro/register.js";
import { getDynamicNames, setDynamicNames } from "@/plugins/permission.js";

const permissionStore = usePermissionStore();
const appStore = useAppStore();
const tagsViewStore = useTagsViewStore();
const dashboardPath = ref("/dashboard");
const navData = computed(() => {
  const d = permissionStore.getNavData();
  return d.filter((item) => item.name !== "主页");
});

const swith = ref("0px");
const sheight = ref("0px");
let resizeObserver = null;
const dialogVisible = ref(false);
const appliList = ref([]);
const qMenuList = ref([]);
const allMenuList = ref([]);
onMounted(() => {
  handleResize();
  getqMenuList();
  getAppliList();
});

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});

// 监听侧边栏宽度变化，保持二级菜单始终停靠在右侧
function handleResize() {
  const sw = document.querySelector(".sidebar-container");
  resizeObserver = new ResizeObserver((entries) => {
    entries.forEach((entry) => {
      const { width } = entry.contentRect;
      console.log("sw", width);

      swith.value = sw.offsetWidth + "px";
    });
  });
  if (sw) {
    resizeObserver.observe(sw);
  }
}
function handleMouseMove(event) {
  // 有可能 target 是子元素（比如 div.menu-title），所以往上找 menu-first 容器
  const menuItem = event.target.closest(".menu-first");
  if (!menuItem) return;

  const rect = menuItem.getBoundingClientRect();
  const centerY = rect.top;
  sheight.value = `${centerY}px`;
}

async function getqMenuList() {
  const res = await appStore.getSidebarLnk();
  if (res.code === 200) {
    const data = res.data;
    if (data && data[0]) {
      const lnkcontent = data[0].lnkcontent;
      const menuList = res.data ? JSON.parse(lnkcontent) : [];
      menuList.forEach((menu) => {
        if (menu.icon.includes("el-icon")) {
          const temp = menu.icon.replace("el-icon-", "");
          menu.vue3Icon = temp
            .split("-")
            .map((item) => {
              return item.charAt(0).toUpperCase() + item.slice(1);
            })
            .join("");
        }
        if (menu.icon.includes("el-icon3")) {
          menu.vue3Icon = menu.icon.replace("el-icon3-", "");
        }
      });
      qMenuList.value = menuList;
    }
  }
}
function openMenuDialog() {
  request
    .get(`/system/SYSM02B2/getWebLnkListBySelf?fncode=` + appStore.config.deffncode)
    .then((res) => {
      if (res.data.code == 200) {
        var arr = [],
          data = res.data.data;
        for (var i = 0; i < data.length; i++) {
          var Item = data[i];
          var obj = {
            key: Item.navid,
            label: Item.navname,
            link: Item.mvcurl,
            background: Item.imagestyle,
            icon: Item.imagecss,
          };
          arr.push(obj);
        }
        allMenuList.value = arr;
        dialogVisible.value = true;
      }
    });
}
async function getAppliList() {
  const res = await appStore.getSidebarBridge();
  if (res.code == 200) {
    let list = res.data;
    for (let i = 0; i < list.length; i++) {
      if (list[i].functioncode == appStore.config.deffncode) {
        list.splice(i, 1);
      }
    }
    appliList.value = list;
  }
}
function routerTo(path) {
  path ? router.push(path) : "";
}

// 保存
function savetranser(data) {
  const fncode = localStorage.getItem("inks-fncode") || appStore.config.deffncode;
  const obj = {
    fncode,
    lnkcontent: data,
  };
  request.post("/system/SYSM07B14/update", JSON.stringify(obj)).then((res) => {
    const response = res.data;
    if (response.code === 200) {
      // 更新localStorage
      response.data = [response.data];
      localStorage.setItem("inks-lnk", JSON.stringify(response));
      getqMenuList();
      ElMessage.success("操作成功");
      dialogVisible.value = false;
    }
  });
}

//菜单读取
function readnav(fncode = appStore.config.deffncode) {
  return new Promise((resolve, reject) => {
    let baseurl = "/system/SYSM02B2/getMenuWebListBySelf";
    if (fncode) {
      baseurl += "?fncode=" + fncode;
    }
    request.get(baseurl).then((response) => {
      if (response.data.code == 200) {
        // 将导航写入状态
        let navjson = response.data.data;
        permissionStore.setNavData(navjson);
        resolve(navjson);
      } else {
        ElMessage.warning(response.data.msg || "获取菜单失败");
        reject(response.data.msg);
      }
    });
  });
}
const appmask = ref(null);
function updateRouter(info) {
  const { functioncode, functionname } = info;
  appStore.config.deffncode = functioncode;
  localStorage.setItem("inks-fncode", functioncode);
  localStorage.setItem("inks-appconfig", JSON.stringify(appStore.config));
  localStorage.removeItem("inks-lnk");
  appmask.value.style.left = "-1000px";
  setTimeout(() => {
    appmask.value.removeAttribute("style");
  }, 100);
  Promise.allSettled([readnav(functioncode), getqMenuList()]).then(() => {
    readnav(functioncode).then(async () => {
      getAppliList();
      console.log("卸载路由");
      getDynamicNames().forEach((name) => {
        router.removeRoute(name);
      });
      tagsViewStore.delAllViews().then((res) => {
        tagsViewStore.toLastView(res.visitedViews);
      });
      console.log("卸载微应用");
      destroyMicroApps();
      // 生成动态路由
      const routeNames = [];
      const dynamicRoutes = await permissionStore.generateRoutes();
      const pageRoutes = dynamicRoutes.filter((item) => item.path === "/");
      const btnRoutes = dynamicRoutes.filter((item) => item.path !== "/");
      pageRoutes.forEach((route) => {
        router.addRoute(route);
        routeNames.push(route.name);
      });
      btnRoutes.forEach((route) => {
        if (route.path.at(0) === "/") {
          route.path = route.path.substring(1);
        }
        router.addRoute("/", route);
        routeNames.push(route.name);
      });
      setDynamicNames(routeNames);
      const allRoutes = router.getRoutes();
      const microRoutes = allRoutes
        .filter((item) => {
          const { ismicroapp, microappname } = item.meta || {};
          return ismicroapp && microappname;
        })
        .map((route) => route.meta || {});
      console.log("子应用列表", microRoutes);
      setupMicroApps(microRoutes);
      router.push({
        path: "/dashboard",
      });
      ElMessage.success(`切换${functionname}成功`);
    });
  });
}
</script>
<style lang="scss" scoped>
.home-title {
  height: 50px;
  padding-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
  line-height: 50px;
  color: #fff;
  text-align: center;
  cursor: pointer;
  background-color: var(--inks-master-color);

  &:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
}

.icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  font-size: 20px;
  background-color: #ccc;
}

.menu-first-item .icon {
  margin-right: 0;
  margin-bottom: 2px;
  background-color: transparent;
}

.login-ul {
  max-height: 300px;
  overflow-y: auto;

  .login-item {
    display: flex;
    align-items: center;
    padding: 20px 10px;
    margin-bottom: 10px;
    cursor: pointer;
    border: 1px solid #efefef;
    border-radius: 4px;

    &:hover {
      background-color: #efefef;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .icon {
      width: 30px;
      height: 30px;
      background-color: #efefef;
    }

    .login-name {
      font-size: 14px;
    }
  }
}

.mask {
  position: fixed;
  top: v-bind(sheight);
  left: v-bind(swith);
  display: none;
  padding-left: 6px;
}

.mask-b {
  position: absolute;
  bottom: 0;
  left: v-bind(swith);
  display: none;
  padding-left: 10px;

  .fast {
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .login-ul {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }

    .login-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 45%;
      border: none;

      .icon {
        margin-bottom: 10px;
      }
    }
  }

  .login-list-wrap {
    box-sizing: border-box;
    width: 350px;
    padding: 10px;
    background-color: #fff;
    box-shadow: 0 3px 5px #efefef;

    .title {
      margin-bottom: 10px;
      font-size: 14px;
      font-weight: bold;
    }
  }
}

.container_ul {
  overflow-x: hidden;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }
}

.menu-first {
  position: relative;

  &:hover {
    background-color: rgba(0, 0, 0, 0.3);

    .mask {
      display: flex;
    }
  }
}

.menu-first-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
}

.menu-second-wrap {
  display: flex;
  gap: 10px;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  box-shadow: 0 3px 5px #00000026;
}

.menu-second-name {
  padding: 5px 38px;
  font-weight: bold;
  border-bottom: 1px solid #ccc;
}

.menu-second {
  padding: 5px 38px;
}

.menu-scond-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  cursor: pointer;

  .menu-scond-item-name:hover {
    color: #0ae;
  }
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: calc(100% - 50px);

  & > ul {
    width: 100%;
  }
}

.control {
  width: 100%;
  margin-bottom: 20px;
  & > ul {
    width: 100%;

    & > li {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 5px 10px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.3);

        .mask-b {
          display: block;
        }
      }
    }
  }
}

.custom-menu-container {
  height: 100%;
  background: var(--inks-master-color);
}

.bodyItem {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 100%;
  height: 80px;
  padding: 4px;
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  .iconStyle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }

  i {
    font-size: 28px;
    line-height: 40px;
    color: #f8f8f8;
  }

  .bodyItem_title {
    max-width: 150px;
    margin-left: 10px;
  }

  &:hover {
    background: #ece8e8;
  }
}

.qmenu-body {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  overflow-x: hidden;
  overflow-y: hidden;
  cursor: pointer;

  .qmenu-bodyItem {
    box-sizing: border-box;
    width: 90px;
    height: 80px;
    padding: 4px;
    margin-top: 15px;
    text-align: center;
    border-radius: 4px;

    .iconStyle {
      width: 40px;
      height: 40px;
      margin: 4px auto;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    i {
      font-size: 28px;
      line-height: 40px;
      color: #f8f8f8;
    }

    .qmenu-title {
      margin: 0;
    }

    &:hover {
      background: #ece8e8;
    }
  }
}
</style>
