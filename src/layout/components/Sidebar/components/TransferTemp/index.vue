<template>
  <div class="transfertemp">
    <el-transfer
      v-model="transferVal"
      filterable
      :titles="titles"
      target-order="push"
      :left-default-checked="leftCheckArr"
      :right-default-checked="rightCheckArr"
      :data="data"
      @left-check-change="leftCheck"
      @right-check-change="rightCheck"
    >
      <template #default="{ option }">
        <span>{{ option.label }}</span>
      </template>

      <template #right-footer>
        <el-button
          class="transfer-footer"
          size="small"
          :disabled="rightCheckArr.length !== 1"
          @click="getMoveUp('right')"
        >
          上移
        </el-button>
        <el-button
          class="transfer-footer"
          size="small"
          :disabled="rightCheckArr.length !== 1"
          @click="getMoveDown('right')"
        >
          下移
        </el-button>
        <el-button class="transfer-footer" size="small" type="primary" @click="handleChange()">
          确认
        </el-button>
      </template>
    </el-transfer>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";

// 定义接口类型
interface TransferItem {
  key: string | number;
  label: string;
  disabled?: boolean;
}

// 定义Props类型
interface Props {
  titles?: string[];
  data?: TransferItem[];
  leftVal?: TransferItem[]; // 暂未用到 弃用
  rightVal?: TransferItem[]; // transferVal初始化数据来源
}

// 定义Props，使用withDefaults提供默认值
const props = withDefaults(defineProps<Props>(), {
  titles: () => ["列表1", "列表2"],
  data: () => [],
  leftVal: () => [],
  rightVal: () => [],
});

// 定义Emits
const emit = defineEmits<{
  savetranser: [value: TransferItem[]];
}>();

// 响应式数据
const transferVal = ref<(string | number)[]>([]);
const leftCheckArr = ref<(string | number)[]>([]);
const rightCheckArr = ref<(string | number)[]>([]);

// 初始化数据
const bindData = () => {
  transferVal.value = [];
  for (let i = 0; i < props.rightVal.length; i++) {
    const item = props.rightVal[i];
    transferVal.value.push(item.key);
  }
};

// 监听rightVal变化，自动重新绑定数据
watch(
  () => props.rightVal,
  () => {
    bindData();
  },
  { deep: true }
);

// 组件挂载时初始化数据
onMounted(() => {
  bindData();
});
// 处理确认按钮点击事件
const handleChange = () => {
  const obj: TransferItem[] = [];

  for (let i = 0; i < transferVal.value.length; i++) {
    const item = transferVal.value[i];
    props.data.forEach((dataItem) => {
      if (dataItem.key === item) {
        obj.push(dataItem);
      }
    });
  }

  emit("savetranser", obj);
};

// 左侧选择变化
const leftCheck = (val: (string | number)[]) => {
  leftCheckArr.value = val;
};

// 右侧选择变化
const rightCheck = (val: (string | number)[]) => {
  rightCheckArr.value = val;
};
// 上移功能
const getMoveUp = (type: "left" | "right") => {
  let index: number;

  if (type === "left") {
    index = transferVal.value.findIndex((a) => a === leftCheckArr.value[0]);
  } else {
    index = transferVal.value.findIndex((a) => a === rightCheckArr.value[0]);
  }

  console.log(index);

  if (index !== -1) {
    if (index === 0) {
      ElMessage.warning("已经是第一行了！");
      return;
    }

    const row = transferVal.value[index];
    transferVal.value.splice(index, 1);
    transferVal.value.splice(index - 1, 0, row);
  }
};

// 下移功能
const getMoveDown = (type: "left" | "right") => {
  let index: number;

  if (type === "left") {
    index = transferVal.value.findIndex((a) => a === leftCheckArr.value[0]);
  } else {
    index = transferVal.value.findIndex((a) => a === rightCheckArr.value[0]);
  }

  if (index !== -1) {
    if (index === transferVal.value.length - 1) {
      ElMessage.warning("已经是最后一行了！");
      return;
    }

    const row = transferVal.value[index];
    transferVal.value.splice(index, 1);
    transferVal.value.splice(index + 1, 0, row);
  }
};
</script>
<style scoped lang="scss">
.transfertemp {
  height: 100%;
  .el-transfer {
    text-align: center;
    height: 100%;
    :deep(.el-transfer-panel__body) {
      height: 500px;
    }
    :deep(.el-transfer-panel:first-child) {
      .el-transfer-panel__body {
        height: 540px;
      }
    }
    :deep(.el-transfer-panel__footer) {
      text-align: center;
    }
  }
}
</style>
