<template>
  <div :class="{ 'has-logo': sidebarLogo }">
    <SidebarMenuCustom />
  </div>
</template>
<script lang="ts">
export default {
  name: "Sidebar",
};
</script>
<script setup lang="ts">
import { useSettingsStore } from "@/store";

const settingsStore = useSettingsStore();

const sidebarLogo = computed(() => settingsStore.sidebarLogo);
</script>

<style lang="scss" scoped>
.has-logo {
  .el-scrollbar {
    height: calc(100vh - $navbar-height);
  }
}
</style>
