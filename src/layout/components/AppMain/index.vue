<template>
  <section class="app-main" :style="{ height: appMainHeight }">
    <router-view>
      <template v-if="showAlive" #default="{ Component, route }">
        <transition enter-active-class="animate__animated animate__fadeIn" mode="out-in">
          <keep-alive v-if="route.meta?.keepAlive">
            <component :is="Component" :key="route.path" />
          </keep-alive>
          <component :is="Component" v-else :key="route.path" />
        </transition>
      </template>
    </router-view>
  </section>
</template>

<script setup lang="ts">
import { useSettingsStore } from "@/store";
import variables from "@/styles/variables.module.scss";

let showAlive = ref(true);
const route = computed(() => useRoute());
watch(
  () => route,
  () => {
    showAlive.value = !route.value.meta.sync;
  },
  {
    deep: true,
    immediate: true,
  }
);

// 缓存页面集合
// const cachedViews = computed(() => useTagsViewStore().cachedViews);
const appMainHeight = computed(() => {
  if (useSettingsStore().tagsView) {
    return `calc(100vh - ${variables["navbar-height"]} - ${variables["tags-view-height"]})`;
  } else {
    return `calc(100vh - ${variables["navbar-height"]})`;
  }
});
</script>

<style lang="scss" scoped>
.app-main {
  position: relative;
  overflow-y: hidden;
  background-color: var(--el-bg-color-page);
  & > div {
    height: 100%;
  }
}
</style>
