import { useAppStore, useUserStore } from "@/store";
function buryingPointAjax(data) {
  const app = useAppStore();
  const xhrURL =
    app.config.env == "dev"
      ? "http://dev.inksyun.com:30471/S21M07B1/tracking" //本地
      : "https://regapi.inksyun.com/S21M07B1/tracking"; //公网
  return new Promise(() => {
    // 创建ajax请求
    const xhr = new XMLHttpRequest();
    xhr.open("post", xhrURL, true);
    xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
    // 发送数据
    xhr.send(data);
  });
}
/**
 * SetTrackingLog 埋点事件
 * code 事件编码
 * desc 事件说明
 * v-TrackingLog="{ code: 'login_submit' }"
 * this.$SetTrackingLog('login_fail', res.data.msg,'/SaUser/login')
 */
function SetTrackingLog(code, desc, pagepath) {
  const app = useAppStore();
  const userinfo = useUserStore().userInfo;
  var saTrackinglogPojo = {
    eventcode: code,
    pagepath: pagepath ? pagepath : "",
    description: desc ? desc : "",
    app: app.config.title,
    env: app.config.env,
    // userinfo: "",
  };
  if (userinfo) {
    saTrackinglogPojo = Object.assign(saTrackinglogPojo, userinfo);
    if (userinfo.tenantinfo) {
      saTrackinglogPojo.company = userinfo.tenantinfo.company;
    }
    //saTrackinglogPojo.userinfo=store.getters.userinfo
  }
  // console.log("saTrackinglogPojo", saTrackinglogPojo);
  buryingPointAjax(JSON.stringify(saTrackinglogPojo));
}

export default SetTrackingLog;
