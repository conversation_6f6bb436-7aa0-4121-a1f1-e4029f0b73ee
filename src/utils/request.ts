/**
 * @file request.ts
 * @description 发送请求封装
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-23 caoshi 加入baseApi，通过localStorage读取baseURL,修改响应拦截器code===200
 * 2025-04-26 caoshi 加入403判断，handleTokenRefresh方法换取token,加入getBaseUrl，浏览器刷新第一次后可访问baseApi
 * 2025-04-30 caoshi loadappdata改为 loadconifg
 */
import axios, { type InternalAxiosRequestConfig, type AxiosResponse } from "axios";
import qs from "qs";
import { useUserStoreHook } from "@/store/modules/user.store";
import { ResultCode } from "@/enums/common/result.enum";
import { getAccessToken } from "@/utils/auth";
import router from "@/router";
import { getBaseUrl } from "./loadconifg";
import { microClear } from "@/micro";
// 刷新 Token 的锁
let isRefreshing = false;
// 因 Token 过期导致失败的请求队列
let requestsQueue: Array<() => void> = [];
let baseApi = "";
try {
  const inksApp = localStorage.getItem("inks-appconfig");
  if (inksApp) {
    baseApi = JSON.parse(inksApp)?.baseURL || "";
  } else {
    baseApi = await getBaseUrl();
  }
} catch (error) {
  console.error("inks-app 配置格式错误", error);
}
// 创建 axios 实例
const service = axios.create({
  baseURL: baseApi,
  timeout: 50000,
  headers: { "Content-Type": "application/json;charset=utf-8" },
  paramsSerializer: (params) => qs.stringify(params),
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const accessToken = getAccessToken();
    const authHeader = config.headers?.Authorization || config.headers?.authorization;
    // 如果 Authorization 设置为 no-auth，则不携带 Token
    if (authHeader === "no-auth") {
      delete config.headers.Authorization;
    } else if (accessToken) {
      config.headers.Authorization = `${accessToken}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, msg } = response.data;
    if (code === 200) {
      return response;
    } else if (code < 200 && code > 300) {
      ElMessage.error(msg || "错误");
      return Promise.reject(new Error(msg || "Error"));
    } else {
      if (code === 403) {
        ElMessage.error(msg || "请求被拒绝");
        return Promise.reject(new Error(msg || "拒绝访问"));
      } else if (code === 401) {
        return handleTokenRefresh(response.config);
      }
      ElMessage.error(msg || "系统出错");
      return Promise.reject(new Error(msg || "Error"));
    }
  },

  async (error: any) => {
    // 非 2xx 状态码处理
    const response = error.response;
    if (response) {
      const { code, msg } = response.data;
      if (code === ResultCode.ACCESS_TOKEN_INVALID) {
        // ElMessageBox.confirm("当前页面已失效，请重新登录", "提示", {
        //   confirmButtonText: "确定",
        //   cancelButtonText: "取消",
        //   type: "warning",
        // }).then(() => {
        //   const userStore = useUserStoreHook();
        //   userStore.clearSessionAndCache().then(() => {
        //     location.reload();
        //   });
        // });
      } else {
        ElMessage.error(msg || "系统出错");
      }
    }
    return Promise.reject(error.message);
  }
);

export default service;
//短期到期换取token
function handleTokenRefresh(config: InternalAxiosRequestConfig): Promise<AxiosResponse> {
  return new Promise((resolve, reject) => {
    const requestCallback = () => {
      config.headers.Authorization = getAccessToken();
      resolve(service(config));
    };

    requestsQueue.push(requestCallback);

    if (!isRefreshing) {
      isRefreshing = true;

      useUserStoreHook()
        .refreshToken()
        .then(() => {
          // 刷新成功，执行队列中的请求
          requestsQueue.forEach((callback) => callback());
          requestsQueue = [];
          resolve(service(config)); // 解决当前请求
        })
        .catch((error) => {
          console.error(error);
          ElNotification({
            title: "提示",
            message: "您的会话已过期，请重新登录",
            type: "info",
          });
          useUserStoreHook()
            .clearUserData()
            .then(() => {
              microClear();
              router.push("/login");
            })
            .catch((err) => {
              console.error("清除用户数据失败", err);
            });
          reject(error); // 拒绝当前请求
        })
        .finally(() => {
          isRefreshing = false;
        });
    } else {
      // 如果正在刷新 Token，则等待刷新完成
      resolve(
        new Promise((res) => {
          requestsQueue.push(() => res(service(config)));
        })
      );
    }
  });
}
