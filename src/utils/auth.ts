/**
 * @file auth.ts
 * @description TOKEN相关参数
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-22 caoshi 加入refresh_token、rtKey，加入setRtKey
 * 2025-04-25 caoshi clearToken加入其他参数
 */
import request from "@/utils/request";

// 访问 token 缓存的 key
const ACCESS_TOKEN_KEY = "access_token";
// 刷新 token 缓存的 key
const REFRESH_TOKEN_KEY = "refresh_token";
// 单点登录中转key relay_key 2025-04-22 新加入
const rtKey = "rtkey";

function getAccessToken(): string {
  return localStorage.getItem(ACCESS_TOKEN_KEY) || "";
}

function setAccessToken(token: string) {
  localStorage.setItem(ACCESS_TOKEN_KEY, token);
}
function clearToken() {
  localStorage.removeItem(ACCESS_TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  localStorage.removeItem(rtKey);
}

/**
 * 新加入
 * RefreshToken、RtKey的set get方法 2025-04-22
 */
function getRefreshToken(): string {
  return localStorage.getItem(REFRESH_TOKEN_KEY) || "";
}

function setRefreshToken(token: string) {
  localStorage.setItem(REFRESH_TOKEN_KEY, token);
}
function setRtKey(token: string) {
  localStorage.setItem(rtKey, token);
}
function getRtKey() {
  return localStorage.getItem(rtKey) || "";
}

// 判断传入密码是否为弱密码
function isWeakPassword(val: string) {
  return new Promise((resolve, reject) => {
    const params = { password: val };
    request
      .post("/system/SYSM07B17/isWeakPassword", JSON.stringify(params))
      .then((res) => {
        resolve(res.data.data);
      })
      .catch((error) => {
        console.log(error || "弱密码校验失败");
        reject(false);
      });
  });
}

export {
  getAccessToken,
  setAccessToken,
  clearToken,
  getRefreshToken,
  setRefreshToken,
  getRtKey,
  setRtKey,
  isWeakPassword,
};
