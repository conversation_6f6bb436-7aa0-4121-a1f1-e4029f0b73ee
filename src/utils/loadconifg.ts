/**
 * @file loadconifg.ts
 * @description 用于加载appload中的参数
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-30 caoshi 加入getConfig、setConfig,loadConfig抽出
 */

let configJson = null;
interface ConfigJson {
  baseURL: string;
  title: string;
  deffncode: string;
  model: string;
  env: string;
  oamapi: string;
}
async function getBaseUrl() {
  const res = await fetch("/appconfig.json");
  configJson = await res.json();
  return configJson.baseURL;
}
async function loadConfig() {
  const { useAppStore } = await import("@/store");
  const appStore = useAppStore();
  try {
    const res = await fetch("/appconfig.json");
    const configJson: ConfigJson = await res.json();
    appStore.config = configJson;
  } catch (err) {
    console.error("获取配置失败:", err);
  }
  const inksFncode = localStorage.getItem("inks-fncode");
  if (inksFncode) {
    appStore.config.deffncode = inksFncode;
    localStorage.setItem("inks-appconfig", JSON.stringify(appStore.config));
  } else {
    localStorage.setItem("inks-fncode", appStore.config.deffncode);
    localStorage.setItem("inks-appconfig", JSON.stringify(appStore.config));
  }

  // 动态ico
  const link = document.createElement("link");
  link.rel = "icon";
  link.href = appStore.config.appico || "/favicon.ico";
  document.head.appendChild(link);

  if (import.meta.env.MODE === "production") {
    if (appStore.config.env !== "dev") {
      console.log = () => {};
    }
  }
}
/**
 *
 * @returns appconfig.json的配置
 */
async function getConfig() {
  const res = await fetch("/appconfig.json");
  configJson = await res.json();
  return configJson;
}
/**
 *
 * @param configJson appconfig.json的配置
 */
async function setConfig(configJson: ConfigJson) {
  const { useAppStore } = await import("@/store");
  const appStore = useAppStore();
  appStore.config = configJson;
  localStorage.setItem("inks-appconfig", JSON.stringify(configJson));
}

export { getBaseUrl, getConfig, setConfig, loadConfig };
