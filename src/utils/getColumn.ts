import request from "@/utils/request";

let customList: any[] = []; // SPU data
let goodsCustomList: {
  displaymark: number;
  itemcode: string;
  itemname: string;
  minwidth: string;
  overflow: number;
  editmark: number;
}[] = []; // Custom goods content
let costgroupList: any[] = []; // Cost group data
let partGroup: any[] = []; // Part group data

// Fetch SPU attributes
async function getListByShow(useDomain: any) {
  try {
    const res = await request.get(`/goods/D91M01S2/getListByShow?ud=${useDomain || ""}`);
    if (res.data.code === 200) {
      customList = res.data.data;
    } else {
      customList = [];
    }
  } catch (error) {
    console.error("Failed to fetch SPU attributes:", error);
    customList = [];
  }
}

// Fetch custom goods content
async function setgoodsCustom(key = "d91m01") {
  goodsCustomList = [];
  try {
    const res = await request.get(`/system/SYSM07B6/getListByCode?key=${key}`);
    if (res.data.code === 200) {
      if (res.data.data.length === 0) {
        // Initialize with default values if no data is returned
        for (let i = 0; i < 10; i++) {
          goodsCustomList.push({
            displaymark: 0,
            itemcode: `goodscustom${i + 1}`,
            itemname: `自定义${i + 1}`,
            minwidth: "80",
            overflow: 1,
            editmark: 1,
          });
        }
      } else {
        // Map returned data to the desired format
        goodsCustomList = res.data.data.map((item: { columncode: any; itemlabel: any }) => ({
          itemcode: `goods${item.columncode}`,
          itemname: item.itemlabel,
          minwidth: "80",
          displaymark: 0,
          overflow: 1,
          editmark: 1,
        }));
      }
    }
  } catch (error) {
    console.error("Failed to fetch custom goods content:", error);
    goodsCustomList = [];
  }
}

// Fetch cost group data
async function getGroupPageList() {
  const queryParams = {
    pagenum: 1,
    pagesize: 1000,
    ordertype: 1,
    searchtype: 1,
  };
  try {
    const res = await request.post("/sale/D01M02S1/getGroupPageList", JSON.stringify(queryParams));
    if (res.data.code === 200) {
      partGroup = res.data.data.list;
      costgroupList = partGroup.map((item: { id: any; grpkey: any; grpname: any }) => ({
        partgroupid: item.id,
        attrkey: item.grpkey,
        attrname: item.grpname,
      }));
    } else {
      costgroupList = [];
    }
  } catch (error) {
    console.error("Failed to fetch cost group data:", error);
    costgroupList = [];
  }
}

// Compare and merge column data
function contrastObj(dgdata: { item: any[] }, data: any) {
  const orgdata = JSON.parse(JSON.stringify(data));
  const newArr: any = { item: [] };

  try {
    dgdata.item.forEach((n: { itemcode: any }) => {
      const index = orgdata.item.findIndex((a: { itemcode: any }) => a.itemcode === n.itemcode);
      if (index !== -1) {
        orgdata.item[index] = { ...orgdata.item[index], ...n };
        newArr.item.push(orgdata.item[index]);
        orgdata.item.splice(index, 1);
      }
    });
    newArr.item.push(...orgdata.item);
  } catch (error) {
    console.error("Error in contrastObj:", error);
  }

  return newArr;
}

// Main function to fetch and organize column data
export async function getColumn(
  code?: string,
  tableForm?: any,
  showspu?: any,
  showgoods?: any,
  showcost?: any,
  useDomain?: any,
  userInfo?: { isadmin: any; permissions: string | string[] }
) {
  if (showcost) {
    // Check if the user has permission
    showcost = userInfo?.isadmin || userInfo?.permissions?.includes("Bus_OrderCost.Cost");
  }

  // Fetch necessary data based on flags
  if (showspu) await getListByShow(useDomain);
  if (showgoods) await setgoodsCustom();
  if (showcost) await getGroupPageList();

  // Merge fetched data with the initial tableForm
  let colList = { ...tableForm };

  if (showcost) {
    costgroupList.forEach((item) => {
      if (!colList.item.some((col: { itemcode: any }) => col.itemcode === item.attrkey)) {
        colList.item.push({
          itemcode: item.attrkey,
          itemname: item.attrname,
          minwidth: "80",
          displaymark: 1,
          overflow: 1,
          editmark: 1,
        });
      }
    });
  }

  if (showspu) {
    customList.forEach((item) => {
      if (!colList.item.some((col: { itemcode: any }) => col.itemcode === item.attrkey)) {
        const newItem = {
          itemcode: item.attrkey,
          itemname: item.attrname,
          minwidth: "80",
          displaymark: item.listshow,
          overflow: 1,
          editmark: 1,
        };
        if (code === "D01M03P1Item") {
          colList.item.splice(-4, 0, newItem);
        } else {
          colList.item.push(newItem);
        }
      }
    });
  }

  if (showgoods) {
    goodsCustomList.forEach((item) => {
      if (!colList.item.some((col: { itemcode: any }) => col.itemcode === item.itemcode)) {
        colList.item.push(item);
      }
    });
  }

  // Fetch additional bill entity data
  try {
    const res = await request.get(`/system/SYSM07B9/getBillEntityByCode?code=${code}`);
    if (res.data.code === 200 && res.data.data) {
      colList = contrastObj(res.data.data, colList);
      colList.id = res.data.data.id;
      colList.formcode = res.data.data.formcode;
    }

    const result = {
      customList,
      colList,
      costgroupList: showcost ? costgroupList : [],
      partGroup: showcost ? partGroup : [],
    };

    return result;
  } catch (error) {
    console.error("服务器请求错误:", error);
    throw new Error("服务器请求错误");
  }
}
