/**
 * @file main.ts
 * @description 入口
 * <AUTHOR>
 * @date 2025-04-23
 * @version 1.0.0
 * @changelog
 * 2025-04-23 加入appconfig全局初始化时加载配置
 * 2025-04-24 加入SvgIcon图标
 */
import { createApp } from "vue";
import App from "./App.vue";
import WujieVue from "wujie-vue3";
import setupPlugins from "@/plugins";
import { loadConfig } from "@/utils/loadconifg";
import "element-plus/dist/index.css";
// 暗黑模式自定义变量
import "@/styles/dark/css-vars.css";
import "@/styles/index.scss";
import "uno.css";
import "virtual:svg-icons-register";
import SvgIcon from "@/components/SvgIcon/index.vue";
import { useVeTable } from "@easytable/vue";
import "@easytable/vue/libs/theme-default/index.css";
import "@easytable/vue/libs/theme-default/index.css";
// 全局引入 animate.css
import "animate.css";

// 自动为某些默认事件（如 touchstart、wheel 等）添加 { passive: true },提升滚动性能并消除控制台的非被动事件监听警告
import "default-passive-events";
const app = createApp(App);

// 注册无界
app.use(WujieVue);
// 注册插件
app.component("SvgIcon", SvgIcon);
app.use(setupPlugins);
app.use(useVeTable());
// 加入appconfig全局初始化时加载配置 2025-04-23
loadConfig();
app.mount("#app");
