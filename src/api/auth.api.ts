/**
 * @file auth.api.ts
 * @description 登录响应
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-22 caoshi 登录接口headers修改、refresh_token修改
 * 2025-04-22 caoshi 修改封装的formData中的账号密码大小写
 * 2025-04-23 caoshi 新增获取租户接口,加入验证码chkCaptcha
 * 2025-04-24 caoshi 加入LoginUser格式化登录回来后的用户名称
 * 2025-04-25 caoshi 加入getSidebarLnk、getSidebarBridge请求访问和CommonResult通用返回
 * 2025-04-30 caoshi 名字改为inks-userinfo
 */
import request from "@/utils/request";
const AUTH_BASE_URL = "/auth";
const AuthAPI = {
  /** 登录接口*/
  login(data: LoginFormData) {
    const formData = new FormData();
    formData.append("UserName", data.username);
    formData.append("Password", data.password);
    // formData.append("captchaKey", data.captchaKey);
    // formData.append("captchaCode", data.captchaCode);
    const res = request<any, LoginResult>({
      url: `${AUTH_BASE_URL}/login`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
    });
    return LoginUser(res);
  },

  /** 刷新 token 接口*/
  refreshToken(refreshToken: string) {
    let tenantid = "";
    try {
      const inksUserInfo = localStorage.getItem("inks-userinfo");
      if (inksUserInfo) {
        tenantid = JSON.parse(inksUserInfo)?.tenantid || "";
      }
    } catch (error) {
      console.error("inks-app 配置格式错误", error);
    }
    return request<any, LoginResult>({
      url: `${AUTH_BASE_URL}/tokenRtKey`,
      method: "post",
      data: {
        refreshtoken: refreshToken,
        tenantid: tenantid,
        // fncode: useAppStore().config.deffncode,
        _isRefresh: true,
      },
      headers: {
        Authorization: "no-auth",
      },
    });
  },

  /** 注销接口*/
  logout() {
    return request({
      url: `${AUTH_BASE_URL}/logout`,
      method: "delete",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
    });
  },

  /** 获取验证码接口*/
  getCaptcha() {
    return request<any, CaptchaResult>({
      url: `${AUTH_BASE_URL}/captchaImage`,
      method: "get",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
    });
  },
  // 获取租户接口 2025-04-23
  getTenant() {
    return request<any, TenantResult>({
      url: `${AUTH_BASE_URL}/getTenant`,
      method: "post",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
    });
  },
  // 验证码校验 2025-04-23
  chkCaptcha(data: string, id: string) {
    return request<any, ChkCaptchaResult>({
      url: `/auth/validateCaptcha?key=${data}&uuid=${id}`,
      method: "get",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
    });
  },
  getSidebarLnk(fncode: string) {
    let url = "/system/SYSM07B14/getListBySelf";
    if (fncode) {
      url += `?fncode=${fncode}`;
    }
    return request<any, CommonResult>({
      url: url,
      method: "get",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
    });
  },
  getSidebarBridge() {
    return request<any, CommonResult>({
      url: `/system/SYSM02B1/getFunctionListBySelf`,
      method: "get",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
    });
  },
};

export default AuthAPI;

/** 登录请求参数 */
export interface LoginFormData {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 验证码缓存key */
  captchaKey: string;
  /** 验证码 */
  captchaCode: string;
}

/** 登录响应 */
export interface LoginResult {
  data: any;
  /** 访问令牌 */
  accessToken: string;
  /** 令牌类型 */
  tokenType: string;
  /** 过期时间(秒) */
  expiresIn: number;
}

/** 验证码响应 */
export interface CaptchaResult {
  data: {
    code: number;
    data: any;
    img: string;
    uuid: string;
  };
}
/** 租户 */
export interface TenantResult {
  data: {
    code: number;
    data: any;
  };
}
/** 通用返回 */
export interface CommonResult {
  data: {
    code: number;
    data: any;
  };
}

/** 验证码校验 */
export interface ChkCaptchaResult {
  data: {
    code: number;
    msg: string;
  };
}

//格式化登录回来后的用户名称
async function LoginUser(res: Promise<LoginResult>) {
  return res;
}
