<template>
  <div>
    <!-- Header 插槽或默认 Header 组件 -->
    <div v-if="slots.Header">
      <slot name="Header" />
    </div>
    <div v-else style="min-height: 150px">
      <Header
        ref="formHeaderRef"
        :formdata="formdata"
        :formtemplate="formtemplate.header.content"
        :title="formtemplate.header.title"
        :selectform="selectform"
        @clickMethods="$emit('clickMethods', $event)"
        @cleValidate="$emit('cleValidate', $event)"
        @getAllGroupName="$emit('getAllGroupName', $event)"
        @getGroupName="$emit('getGroupName', $event)"
        @getSuppGroupName="$emit('getSuppGroupName', $event)"
        @getFactGroupName="$emit('getFactGroupName', $event)"
        @getWorkGroupName="$emit('getWorkGroupName', $event)"
        @getBranGroupName="$emit('getBranGroupName', $event)"
        @getProsGroupName="$emit('getProsGroupName', $event)"
        @getStoreName="getStoreName"
        @getProcName="$emit('getProcName', $event)"
        @getRoleProcName="$emit('getRoleProcName', $event)"
        @getGoodsName="$emit('getGoodsName', $event)"
        @getFlawName="$emit('getFlawName', $event)"
        @autoClear="$emit('autoClear')"
        @autoStoreClear="$emit('autoStoreClear', $event)"
        @autoProcClear="$emit('autoProcClear')"
        @autoRoleProcClear="$emit('autoRoleProcClear')"
        @autoFlawClear="$emit('autoFlawClear')"
        @autoGoodsClear="$emit('autoGoodsClear')"
      />
    </div>

    <!-- Item 插槽 -->
    <slot name="Item" />

    <!-- Footer 插槽或默认 Footer 组件 -->
    <div v-if="slots.Footer">
      <slot name="Footer" />
    </div>
    <div v-else>
      <Footer :formdata="formdata" :formtemplate="formtemplate.footer.content" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref } from "vue";
import Header from "./header.vue";
import Footer from "./footer.vue";

// 定义 props
interface FormData {
  [key: string]: any;
}

interface FormTemplate {
  header: {
    content: any;
    title: string;
  };
  footer: {
    content: any;
  };
}

defineProps<{
  formdata: FormData;
  formtemplate: FormTemplate;
  selectform?: object | any[];
}>();

// 定义 emits
const emit = defineEmits([
  "clickMethods",
  "cleValidate",
  "getAllGroupName",
  "getGroupName",
  "getSuppGroupName",
  "getFactGroupName",
  "getWorkGroupName",
  "getBranGroupName",
  "getProsGroupName",
  "getStoreName",
  "getProcName",
  "getRoleProcName",
  "getGoodsName",
  "getFlawName",
  "autoClear",
  "autoStoreClear",
  "autoProcClear",
  "autoRoleProcClear",
  "autoFlawClear",
  "autoGoodsClear",
]);

// 获取插槽
const slots = useSlots();

// 用于获取 Header 组件的 ref
const formHeaderRef = ref();

// 处理 getStoreName 事件
const getStoreName = (data: any, file: any) => {
  emit("getStoreName", data, file);
};
</script>
