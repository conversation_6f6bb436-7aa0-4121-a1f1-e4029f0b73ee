<template>
  <div>
    <!-- 保存按钮 -->
    <el-button
      v-show="showcontent.includes('save')"
      type="primary"
      :disabled="formstate === 2 || submitting === 1 || !!formdata?.oaflowmark"
      @click="submitForm('formdata')"
    >
      保 存
    </el-button>

    <!-- 审核/反审核按钮 -->
    <el-button
      v-show="showcontent.includes('approval')"
      type="primary"
      :disabled="formstate === 0 || submitting === 1 || !!formdata?.oaflowmark"
      style="margin-left: 10px"
      @click="approval"
    >
      {{ !formdata?.assessor ? "审核" : "反审核" }}
    </el-button>

    <!-- 打印按钮 -->
    <el-button
      v-show="showcontent.includes('print')"
      :disabled="formstate === 0 || submitting === 1"
      type="primary"
      style="margin-left: 10px"
      @click="printButton"
    >
      打 印
    </el-button>

    <!-- 操作下拉菜单 -->
    <el-dropdown
      v-if="showcontent.includes('operate')"
      trigger="click"
      placement="bottom"
      style="margin-left: 10px"
      :hide-on-click="false"
    >
      <el-button>
        操作
        <el-icon style="margin-left: 6px"><ArrowDown /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <div v-for="(item, index) in visibleOperateBar" :key="index">
            <el-dropdown-item
              v-if="!item.children || item.children.length === 0"
              :divided="item.divided"
              :disabled="checkDisabled(item.disabled)"
              @click="clickMethods(item.methods, item.param)"
            >
              {{ returnEval(item.label, item.ieval) }}
            </el-dropdown-item>
            <template v-else>
              <el-dropdown-item v-show="item.show" :divided="item.divided">
                <el-dropdown trigger="click" placement="left-start">
                  <span>{{ returnEval(item.label, item.ieval) }}</span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <!-- 渲染子项 -->
                      <el-dropdown-item
                        v-for="(childItem, num) in item.children"
                        v-show="childItem.show"
                        :key="num"
                        :divided="childItem.divided"
                        :disabled="formstate === 0"
                        @click="clickMethods(childItem.methods, childItem.param)"
                      >
                        {{ returnEval(childItem.label, childItem.ieval) }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-dropdown-item>
            </template>
          </div>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 过程下拉菜单 -->
    <el-dropdown
      v-show="showcontent.includes('process')"
      trigger="click"
      placement="bottom"
      style="margin-left: 10px"
    >
      <el-button>
        过程
        <el-icon style="margin-left: 6px"><ArrowDown /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <div v-for="(item, index) in visibleProcessBar" :key="index">
            <div v-if="(item.children ?? []).length === 0">
              <el-dropdown-item
                v-show="item.show"
                :divided="item.divided"
                :disabled="returnEval(item.disabled, 1)"
                @click="clickMethods(item.methods, item.param)"
              >
                {{ returnEval(item.label, item.ieval) }}
              </el-dropdown-item>
            </div>
            <div v-else>
              <el-dropdown-item v-show="item.show" :divided="item.divided">
                <el-dropdown trigger="click" placement="left-start">
                  <span>{{ returnEval(item.label, item.ieval) }}</span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="(childItem, num) in item.children"
                        v-show="childItem.show"
                        :key="num"
                        :divided="childItem.divided"
                        :disabled="formstate === 0"
                        @click="clickMethods(childItem.methods, childItem.param)"
                      >
                        {{ returnEval(childItem.label, childItem.ieval) }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-dropdown-item>
            </div>
          </div>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 关闭按钮 -->
    <el-button style="margin-left: 10px" @click="closeForm">关 闭</el-button>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
// 定义子项类型
interface DropdownItem {
  label: string;
  method?: string;
  param?: any;
  show: boolean;
  divided: boolean;
  disabled?: string | boolean;
  ieval?: boolean;
  methods?: string;
  children?: DropdownItem[];
}

defineOptions({
  name: "EditGroupBtns",
});

const pops = defineProps({
  showcontent: {
    type: Array,
    default: () => ["save", "approval", "print", "operate", "process"],
  },
  formdata: {
    type: Object,
    required: true,
  },
  operateBar: {
    type: Array as () => DropdownItem[],
    required: false,
  },
  processBar: {
    type: Array as () => DropdownItem[],
    required: false,
  },
  formstate: {
    type: Number,
    default: 0,
  },
  submitting: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["clickMethods", "submitForm", "approval", "printButton", "closeForm"]);
const visibleOperateBar = computed(() => (pops.operateBar ?? []).filter((item) => item.show));
const visibleProcessBar = computed(() => (pops.processBar ?? []).filter((item) => item.show));
// 处理评估方法
function returnEval(data: any, itrue: any) {
  return itrue ? eval(data) : data;
}

function checkDisabled(itemDisabled: any) {
  if (itemDisabled) return eval(itemDisabled);
}
// 点击操作方法
function clickMethods(meth: any, param: any) {
  emit("clickMethods", { meth, param });
}

// 表单提交
function submitForm(formName: string) {
  emit("submitForm", formName);
}

// 审核
function approval() {
  emit("approval");
}

// 打印按钮
function printButton() {
  emit("printButton");
}

// 关闭表单
function closeForm() {
  emit("closeForm");
}
</script>

<style scoped lang="scss">
.hamburger {
  vertical-align: middle;
  cursor: pointer;
  transform: scaleX(-1);
}

.hamburger.is-active {
  transform: scaleX(1);
}
</style>
