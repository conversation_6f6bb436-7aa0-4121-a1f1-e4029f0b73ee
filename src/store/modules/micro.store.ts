import { store } from "@/store";

export const useMicroStore = defineStore("micro", () => {
  // micro-tabs数据缓存
  const microCache = useStorage("microCache", {});
  const microState = useStorage("microState", {});
  /**
   * 缓存字典数据
   * @param key 字典编码
   * @param data 字典项列表
   */
  const cacheMicroItem = (key: string, data: any) => {
    microCache.value[key] = data;
  };
  /**
   * 获取字典项列表
   * @param dictCode 字典编码
   * @returns 字典项列表
   */
  const getMicroItem = (dictCode: string): any => {
    return microCache.value[dictCode];
  };

  const clearMicroState = () => {
    microState.value = {};
  };

  const setMicroState = (key: string, data: any) => {
    microState.value[key] = data;
  };

  const getMicroState = (key: string): any => {
    return microState.value[key];
  };
  return {
    cacheMicroItem,
    getMicroItem,
    clearMicroState,
    setMicroState,
    getMicroState,
  };
});

export function useMicroStoreHook() {
  return useMicroStore(store);
}
