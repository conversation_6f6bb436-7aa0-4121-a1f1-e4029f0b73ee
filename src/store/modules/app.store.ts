/**
 * @file app.store.ts
 * @description 应用信息存储
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-22 caoshi 加入导航菜单
 * 2025-04-23 caoshi 加入loadConfig
 * 2025-04-23 caoshi 导航菜单移入permission
 * 2025-04-23 caoshi config中的数据持久化到浏览器
 * 2025-04-24 caoshi getSidebarLnk，getSidebarBridge 快速菜单、功能菜单、config加入额外参数
 * 2025-04-26 caoshi 加入getConfig setConfig、去除插件、siderBarLink、sidebarBridge改为浏览器中没有读取API
 * 2025-04-30 caoshi 持久化存储名字改为inks-bridge、inks-lnk,去除getconfig、setConfig
 */
import defaultSettings from "@/settings";
import AuthAPI from "@/api/auth.api";
// 导入 Element Plus 中英文语言包
import zhCn from "element-plus/es/locale/lang/zh-cn";
import en from "element-plus/es/locale/lang/en";
import { store } from "@/store";
import { DeviceEnum } from "@/enums/settings/device.enum";
import { SidebarStatus } from "@/enums/settings/layout.enum";

export const useAppStore = defineStore("app", () => {
  // 设备类型
  const device = useStorage("device", DeviceEnum.DESKTOP);
  // 布局大小
  const size = useStorage("size", defaultSettings.size);
  // 语言
  let appconfig = localStorage.getItem("inks-appconfig");
  let i18nLang = "";
  if (appconfig) {
    appconfig = JSON.parse(appconfig);
    i18nLang = appconfig.i18n || "";
  }
  const language = useStorage("language", i18nLang);
  // 侧边栏状态
  const sidebarStatus = useStorage("sidebarStatus", SidebarStatus.CLOSED);
  const sidebar = reactive({
    opened: sidebarStatus.value === SidebarStatus.OPENED,
    withoutAnimation: false,
  });

  // 顶部菜单激活路径
  const activeTopMenuPath = useStorage("activeTopMenuPath", "");

  //根据语言标识读取对应的语言包
  const locale = computed(() => {
    if (language?.value == "en") {
      return en;
    } else if (language?.value == "zh-CN") {
      return zhCn;
    } else {
      return zhCn;
    }
  });
  /**
   * 新加入
   * config系统config 2025-4-23
   */

  const config = ref({
    baseURL: "",
    title: "",
    model: "",
    deffncode: "",
    env: "",
    oamapi: "",
  });
  let siderBarLink = null;
  let sidebarBridge = null;
  // 切换侧边栏
  function toggleSidebar() {
    sidebar.opened = !sidebar.opened;
    sidebarStatus.value = sidebar.opened ? SidebarStatus.OPENED : SidebarStatus.CLOSED;
  }

  // 关闭侧边栏
  function closeSideBar() {
    sidebar.opened = false;
    sidebarStatus.value = SidebarStatus.CLOSED;
  }

  // 打开侧边栏
  function openSideBar() {
    sidebar.opened = true;
    sidebarStatus.value = SidebarStatus.OPENED;
  }

  // 切换设备
  function toggleDevice(val: string) {
    device.value = val;
  }

  /**
   * 改变布局大小
   *
   * @param val 布局大小 default | small | large
   */
  function changeSize(val: string) {
    size.value = val;
  }
  /**
   * 切换语言
   *
   * @param val
   */
  function changeLanguage(val: string) {
    language.value = val;
  }
  // 混合模式顶部切换
  function activeTopMenu(val: string) {
    activeTopMenuPath.value = val;
  }

  /**
   * 新加入
   */

  async function getSidebarLnk() {
    let lnkData = "";
    try {
      const inksLnkData = localStorage.getItem("inks-lnk");
      if (inksLnkData) {
        lnkData = JSON.parse(inksLnkData) || "";
      }
    } catch (error) {
      console.error("inks-app 配置格式错误", error);
    }
    if (lnkData) {
      return lnkData;
    } else {
      const fncode = localStorage.getItem("inks-fncode") || config.value.deffncode;
      siderBarLink = AuthAPI.getSidebarLnk(fncode);
      localStorage.setItem("inks-lnk", JSON.stringify((await siderBarLink).data));
      return (await siderBarLink).data;
    }
  }

  async function getSidebarBridge() {
    let bridgeData = "";
    try {
      const inksBridgeData = localStorage.getItem("inks-bridge");
      if (inksBridgeData) {
        bridgeData = JSON.parse(inksBridgeData) || "";
      }
    } catch (error) {
      console.error("inks-app 配置格式错误", error);
    }
    if (bridgeData) {
      return bridgeData;
    } else {
      sidebarBridge = AuthAPI.getSidebarBridge();
      localStorage.setItem("inks-bridge", JSON.stringify((await sidebarBridge).data));
      return (await sidebarBridge).data;
    }
  }

  return {
    device,
    sidebar,
    language,
    locale,
    size,
    config,
    activeTopMenuPath,
    activeTopMenu,
    toggleDevice,
    changeSize,
    changeLanguage,
    toggleSidebar,
    closeSideBar,
    openSideBar,
    getSidebarLnk,
    getSidebarBridge,
  };
});

/**
 * 用于在组件外部（如在Pinia Store 中）使用 Pinia 提供的 store 实例。
 * 官方文档解释了如何在组件外部使用 Pinia Store：
 * https://pinia.vuejs.org/core-concepts/outside-component-usage.html#using-a-store-outside-of-a-component
 */
export function useAppStoreHook() {
  return useAppStore(store);
}
