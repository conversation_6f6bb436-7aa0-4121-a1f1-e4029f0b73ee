/**
 * @file permission.store.ts
 * @description 路由页面信息存储
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-22 caoshi 加入导航菜单
 * 2025-04-24 caoshi navdata持久化到浏览器,加入getNavData
 * 2025-04-25 caoshi 路由数据持久化到浏览器、持久化改为只持久化NavData
 * 2025-04-30 caoshi 持久化存储名字改为inks-menu
 */
import type { RouteRecordRaw } from "vue-router";
import { constantRoutes } from "@/router";
import { store } from "@/store";
import router from "@/router";
import MenuAPI, { type RouteVO } from "@/api/system/menu.api";
import WujieVue from "wujie-vue3";
import { IMicroRoute } from "@/micro/register";
import { getKeyData } from "@/micro";
const modules = import.meta.glob("../../views/**/**.vue");
const Layout = () => import("@/layout/index.vue");
const protocol = window.location.protocol;
export const usePermissionStore = defineStore("permission", () => {
  // 储所有路由，包括静态路由和动态路由
  const routes = ref<RouteRecordRaw[]>([]);
  // 混合模式左侧菜单路由
  const mixedLayoutLeftRoutes = ref<RouteRecordRaw[]>([]);
  // 路由是否加载完成
  const isRoutesLoaded = ref(false);
  /**
   * 新加入
   * navdata 导航2025-4-23
   */
  const navdata = ref<any>([]);
  /**
   * 获取后台动态路由数据，解析并注册到全局路由
   *
   * @returns Promise<RouteRecordRaw[]> 解析后的动态路由列表
   */
  async function generateRoutes() {
    const data = await MenuAPI.getRoutes();
    const routerData = data.data.data;
    const dashboard = routerData.find((item) => item.name === "主页");
    if (dashboard) {
      const home = constantRoutes.find((item) => item.path === "/");
      const row = home?.children?.find((item) => item.path === "dashboard");
      if (row) {
        row.redirect = dashboard.path;
        router.removeRoute(row.name as string);
        router.addRoute("/", row);
        if (dashboard.meta) {
          dashboard.meta.affix = true;
          dashboard.meta.title = row.meta?.title;
        }
        delete row.component;
        delete row.meta?.title;
        delete row.meta?.affix;
      }
    }
    const dynamicRoutes = parseDynamicRoutes(routerData);
    routes.value = [...constantRoutes, ...dynamicRoutes];
    isRoutesLoaded.value = true;
    return dynamicRoutes;
  }

  /**
   * 根据父菜单路径设置混合模式左侧菜单
   *
   * @param parentPath 父菜单的路径，用于查找对应的菜单项
   */
  const setMixedLayoutLeftRoutes = () => {};

  /**
   * 重置路由
   */
  const resetRouter = () => {
    //  从 router 实例中移除动态路由
    routes.value.forEach((route: any) => {
      if (route.name && !constantRoutes.find((r) => r.name === route.name)) {
        router.removeRoute(route.name);
      }
    });

    // 清空本地存储的路由和菜单数据
    routes.value = [];
    mixedLayoutLeftRoutes.value = [];
    isRoutesLoaded.value = false;
  };
  /**
   * 新加入
   * setNavData 菜单赋值 2025-4-23
   */

  function setNavData(data: any) {
    navdata.value = data;
    localStorage.setItem("inks-menu", JSON.stringify(data));
  }
  function getNavData() {
    return navdata.value;
  }
  function initNavData() {
    let nav = [];
    try {
      const inksNavData = localStorage.getItem("inks-menu");
      if (inksNavData) {
        nav = JSON.parse(inksNavData) || "";
      }
    } catch (error) {
      console.error("inks-app 配置格式错误", error);
    }
    navdata.value = nav;
  }

  initNavData();
  return {
    routes,
    mixedLayoutLeftRoutes,
    isRoutesLoaded,
    navdata,
    generateRoutes,
    setMixedLayoutLeftRoutes,
    resetRouter,
    setNavData,
    getNavData,
  };
});
/**
 * 解析后端返回的路由数据并转换为 Vue Router 兼容的路由配置
 *
 * @param rawRoutes 后端返回的原始路由数据
 * @returns 解析后的路由配置数组
 */
const parseDynamicRoutes = (rawRoutes: RouteVO[], rootName?: string): RouteRecordRaw[] => {
  const parsedRoutes: RouteRecordRaw[] = [];
  const degrade: any[] = [];
  const localnet = getKeyData("inks-appconfig")?.localnet;
  rawRoutes.forEach((route) => {
    const normalizedRoute = { ...route } as RouteRecordRaw & IMicroRoute;
    const ismicroapp = normalizedRoute.ismicroapp;
    if (localnet && [1, "1"].includes(localnet)) {
      normalizedRoute.microappentry = normalizedRoute.microapplocal;
    }
    if (ismicroapp === 1 && protocol === "http:") {
      const microappentry = normalizedRoute.microappentry;
      if (microappentry && microappentry.startsWith("https:")) {
        // 降级为http
        degrade.push(Object.assign({}, normalizedRoute));
        normalizedRoute.microappentry = microappentry.replace("https:", "http:");
      }
    }
    // 处理组件路径
    const { microappentry, microappname, microapprule } = normalizedRoute;
    // 微应用
    if (ismicroapp && ismicroapp === 1 && microappname) {
      normalizedRoute.component = WujieVue;
      const microurl = `${microappentry}${microapprule}`;
      normalizedRoute.props = {
        url: microurl,
        name: microappname,
        alive: true,
      };
      if (normalizedRoute.meta) {
        Object.assign(normalizedRoute.meta, {
          ismicroapp,
          microappentry,
          microappname,
          microapprule,
          microurl,
        });
      }
    } else {
      // 本地应用
      const componentPath = normalizedRoute.component?.toString().toLowerCase();

      if (componentPath === "layout" && !normalizedRoute.path.startsWith("/")) {
        normalizedRoute.path = "/" + normalizedRoute.path;
      }

      normalizedRoute.component =
        componentPath === "layout"
          ? Layout
          : modules[`../../views/${normalizedRoute.component}.vue`] ||
            modules["../../views/error-page/404.vue"];
    }

    // 递归解析子路由
    if (normalizedRoute.children) {
      normalizedRoute.children = parseDynamicRoutes(route.children, normalizedRoute.meta.title);
    }
    let path = normalizedRoute.path.replace(/^\//, "");
    if (rootName) {
      normalizedRoute.name = `${rootName}/${normalizedRoute.name as string}/${path}`;
    } else {
      normalizedRoute.name = `${normalizedRoute.name as string}/${path}`;
    }
    parsedRoutes.push(normalizedRoute);
  });
  if (degrade.length) {
    console.warn("降级为http协议的子应用列表", degrade);
  }
  return parsedRoutes;
};
/**
 * 在组件外使用 Pinia store 实例 @see https://pinia.vuejs.org/core-concepts/outside-component-usage.html
 */
export function usePermissionStoreHook() {
  return usePermissionStore(store);
}
