/**
 * @file user.store.ts
 * @description 用户信息存储
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-22 caoshi 加入setUserInfo方法,修改login方法，修改请求结构，加入clearUserData和refreshToken
 * 2025-04-22 caoshi 加入getTenantInfo
 * 2025-04-26 caoshi pinia待久化手工替代插件
 * 2025-04-30 caoshi 持久化存储名字改为inks-userinfo
 */
import { store } from "@/store";
import { usePermissionStoreHook } from "@/store/modules/permission.store";
import { useDictStoreHook } from "@/store/modules/dict.store";
import AuthAPI, { type LoginFormData } from "@/api/auth.api";
import { type UserInfo } from "@/api/system/user.api";
//待优化：不应该在pinia中调用工具类
import { setAccessToken, clearToken, setRefreshToken, getRefreshToken } from "@/utils/auth";

export const useUserStore = defineStore("user", () => {
  const userInfo = useStorage<UserInfo>("userInfo", {} as UserInfo);

  /**
   * 新加入
   * tenantinfo 获取租户 2025-4-22
   */
  const tenantinfo = ref({
    company: "",
    companyadd: "",
    deptname: "",
  });
  /**
   * 登录
   *
   * @param {LoginFormData}
   * @returns
   * 2025/4/23修改格式
   */
  async function login(loginData: LoginFormData) {
    const data = await AuthAPI.login(loginData);
    if (data.data.code == 200) {
      setAccessToken(data.data.data.access_token);
      setRefreshToken(data.data.data.access_token);
      setUserInfo(data.data.data.loginuser);
    }
    return data;
  }

  /**
   * 获取用户信息
   *
   * @returns {UserInfo} 用户信息
   */
  async function getUserInfo() {
    let userInfoData = "";
    try {
      const inksUserInfoData = localStorage.getItem("inks-userinfo");
      if (inksUserInfoData) {
        userInfoData = JSON.parse(inksUserInfoData) || "";
      }
    } catch (error) {
      console.error("inks-app 配置格式错误", error);
    }
    return userInfoData;
  }

  /**
   * 获取用户信息
   *
   * @returns {UserInfo} 用户信息
   */
  function setUserInfo(infodata: UserInfo) {
    if (!infodata.avatar) {
      infodata.avatar = "https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg";
    }
    userInfo.value = infodata;
    localStorage.setItem("inks-userinfo", JSON.stringify(userInfo.value));
  }

  /**
   * 登出
   */
  async function logout() {
    await AuthAPI.logout();
    clearSessionAndCache();
  }

  /**
   * 清除用户会话和缓存
   */
  function clearSessionAndCache() {
    return new Promise<void>((resolve) => {
      clearToken();
      usePermissionStoreHook().resetRouter();
      useDictStoreHook().clearDictCache();
      resolve();
    });
  }

  /**
   * 新加入
   * clearUserData清理用户数据   2025-4-22
   * refreshToken 2025-4-22
   * getTenantInfo 获取租户2025-4-22
   */
  async function clearUserData() {
    clearToken();
    await usePermissionStoreHook().resetRouter();
  }
  //token到期时刷新token
  async function refreshToken() {
    try {
      const refreshToken = getRefreshToken();
      const res = await AuthAPI.refreshToken(refreshToken);
      if (res.data.code === 200) {
        const { access_token, refreshtoken } = res.data.data;
        setAccessToken(access_token);
        setRefreshToken(refreshtoken);
        // // setrtKey(rtkey);
        setUserInfo(res.data.data.loginuser);
      } else {
        throw new Error("Token refresh failed");
      }
    } catch (error) {
      console.error("refreshToken 刷新失败", error);
      throw error;
    }
  }
  //获取租户2025-4-22
  function getTenantInfo(tenantInfo: any) {
    tenantinfo.value = tenantInfo;
  }

  return {
    userInfo,
    setUserInfo,
    getUserInfo,
    login,
    logout,
    clearSessionAndCache,
    refreshToken,
    clearUserData,
    getTenantInfo,
  };
});

/**
 * 用于在组件外部（如在Pinia Store 中）使用 Pinia 提供的 store 实例。
 * 官方文档解释了如何在组件外部使用 Pinia Store：
 * https://pinia.vuejs.org/core-concepts/outside-component-usage.html#using-a-store-outside-of-a-component
 */
export function useUserStoreHook() {
  return useUserStore(store);
}
