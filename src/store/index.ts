/**
 * @file index.ts
 * @description 用于注册pinia
 * <AUTHOR>
 * @date 2025-04-22
 * @version 1.0.0
 * @changelog
 * 2025-04-23 piniaPluginPersistedstate来持久化存储
 * 2025-04-26 去除piniaPluginPersistedstate来持久化存储
 */
import type { App } from "vue";
import { createPinia } from "pinia";

const store = createPinia();
// 全局注册 store
export function setupStore(app: App<Element>) {
  app.use(store);
}

export * from "./modules/app.store";
export * from "./modules/permission.store";
export * from "./modules/settings.store";
export * from "./modules/tags-view.store";
export * from "./modules/user.store";
export * from "./modules/dict.store";
export * from "./modules/micro.store";
export { store };
