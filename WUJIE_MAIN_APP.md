# Wujie 主应用开发文档

## 项目概述

基于 Vue 3 + Vite 构建的 wujie 微前端主应用，用于管理和加载多个子应用。

## 技术栈

- **前端框架**: Vue 3
- **构建工具**: Vite
- **微前端框架**: wujie
- **包管理器**: npm


## 安装依赖

### 1. 安装 wujie

```bash
npm install wujie-vue3
```

## 配置说明

### 1. Vite 配置 (vite.config.js)

```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3000,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
    }
  },
  build: {
    target: 'es2015',
    rollupOptions: {
      external: ['wujie-vue3']
    }
  }
})
```

### 2. <PERSON>jie 配置 (src/config/wujie.js)

```javascript
import { bus } from 'wujie-vue3'

// 子应用配置
export const subApps = [
  {
    name: 'sub-app-1',
    url: 'http://localhost:3001',
    title: '子应用1',
    path: '/sub-app-1'
  },
  {
    name: 'sub-app-2', 
    url: 'http://localhost:3002',
    title: '子应用2',
    path: '/sub-app-2'
  }
]

// wujie 全局配置
export const wujieConfig = {
  // 预加载配置
  preloadApp: {
    name: 'sub-app-1',
    url: 'http://localhost:3001'
  },
  
  // 生命周期钩子
  lifecycle: {
    beforeLoad: (appWindow) => {
      console.log('子应用加载前', appWindow)
    },
    beforeMount: (appWindow) => {
      console.log('子应用挂载前', appWindow)
    },
    afterMount: (appWindow) => {
      console.log('子应用挂载后', appWindow)
    },
    beforeUnmount: (appWindow) => {
      console.log('子应用卸载前', appWindow)
    },
    afterUnmount: (appWindow) => {
      console.log('子应用卸载后', appWindow)
    }
  },
  
  // 通信配置
  communication: {
    // 主应用向子应用传递数据
    props: {
      userInfo: {},
      theme: 'light'
    }
  }
}

// 事件总线 - 用于主子应用通信
export { bus }
```

### 3. 主应用入口配置 (src/main.js)

```javascript
import { createApp } from 'vue'
import { setupApp, preloadApp, bus } from 'wujie-vue3'
import App from './App.vue'
import router from './router'
import { wujieConfig } from './config/wujie'

const app = createApp(App)

// 注册路由
app.use(router)

// 预加载子应用
if (wujieConfig.preloadApp) {
  preloadApp(wujieConfig.preloadApp)
}

// 设置全局通信
app.config.globalProperties.$bus = bus

app.mount('#app')
```

## 核心组件

### 1. Wujie 容器组件 (src/components/WujieApp.vue)

```vue
<template>
  <div class="wujie-container">
    <WujieVue
      :name="name"
      :url="url"
      :sync="sync"
      :props="props"
      :alive="alive"
      :fetch="fetch"
      :plugins="plugins"
      @beforeLoad="handleBeforeLoad"
      @beforeMount="handleBeforeMount"
      @afterMount="handleAfterMount"
      @beforeUnmount="handleBeforeUnmount"
      @afterUnmount="handleAfterUnmount"
    />
  </div>
</template>

<script setup>
import { WujieVue } from 'wujie-vue3'
import { ref, computed } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  url: {
    type: String,
    required: true
  },
  sync: {
    type: Boolean,
    default: true
  },
  alive: {
    type: Boolean,
    default: false
  },
  props: {
    type: Object,
    default: () => ({})
  }
})

// 生命周期处理
const handleBeforeLoad = (appWindow) => {
  console.log(`${props.name} 加载前`, appWindow)
}

const handleBeforeMount = (appWindow) => {
  console.log(`${props.name} 挂载前`, appWindow)
}

const handleAfterMount = (appWindow) => {
  console.log(`${props.name} 挂载后`, appWindow)
}

const handleBeforeUnmount = (appWindow) => {
  console.log(`${props.name} 卸载前`, appWindow)
}

const handleAfterUnmount = (appWindow) => {
  console.log(`${props.name} 卸载后`, appWindow)
}

// 自定义插件
const plugins = [
  {
    // CSS 隔离插件
    cssLoader: (code, url) => {
      // 处理CSS代码
      return code
    },
    // JS 加载插件
    jsLoader: (code, url) => {
      // 处理JS代码
      return code
    }
  }
]

// 自定义fetch
const fetch = (url, options) => {
  return window.fetch(url, {
    ...options,
    headers: {
      ...options?.headers,
      'Authorization': 'Bearer token'
    }
  })
}
</script>

<style scoped>
.wujie-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
```

## 路由配置

### src/router/index.js

```javascript
import { createRouter, createWebHistory } from 'vue-router'
import { subApps } from '../config/wujie'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  // 动态生成子应用路由
  ...subApps.map(app => ({
    path: app.path,
    name: app.name,
    component: () => import('../views/SubAppView.vue'),
    meta: {
      subApp: app
    }
  }))
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
```

## 主子应用通信

### 1. 主应用向子应用传递数据

```javascript
// 通过 props 传递
const appProps = {
  userInfo: {
    id: 1,
    name: '张三',
    role: 'admin'
  },
  theme: 'dark'
}

// 通过事件总线传递
import { bus } from 'wujie-vue3'

bus.$emit('main-to-sub', {
  type: 'USER_INFO_UPDATE',
  data: userInfo
})
```

### 2. 子应用向主应用传递数据

```javascript
// 在子应用中
import { bus } from 'wujie-vue3'

// 监听主应用消息
bus.$on('main-to-sub', (data) => {
  console.log('收到主应用消息:', data)
})

// 向主应用发送消息
bus.$emit('sub-to-main', {
  type: 'ROUTE_CHANGE',
  data: { path: '/new-path' }
})
```

## 开发指南

### 1. 启动开发环境

```bash
# 启动主应用
npm run dev

# 启动子应用（在各自目录下）
cd sub-app-1 && npm run dev
cd sub-app-2 && npm run dev
```

### 2. 构建生产环境

```bash
# 构建主应用
npm run build

# 构建子应用
cd sub-app-1 && npm run build
cd sub-app-2 && npm run build
```

### 3. 调试技巧

1. **开启调试模式**
```javascript
// 在开发环境中开启
if (process.env.NODE_ENV === 'development') {
  window.__WUJIE_DEBUG__ = true
}
```

2. **查看子应用状态**
```javascript
import { getWujieById } from 'wujie-vue3'

const subAppInstance = getWujieById('sub-app-1')
console.log('子应用实例:', subAppInstance)
```

## 最佳实践

### 1. 性能优化

- 使用 `alive` 模式保持子应用状态
- 合理使用预加载功能
- 避免频繁的子应用切换

### 2. 样式隔离

- 使用 CSS Module 或 scoped 样式
- 避免全局样式污染
- 统一设计规范

### 3. 状态管理

- 主应用负责全局状态
- 子应用管理局部状态
- 通过通信机制同步必要数据

### 4. 错误处理

```javascript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error)
})

// 子应用加载失败处理
const handleLoadError = (error) => {
  console.error('子应用加载失败:', error)
  // 显示错误页面或重试机制
}
```

## 常见问题

### 1. 跨域问题
确保子应用服务器配置了正确的 CORS 头部。

### 2. 样式冲突
使用 CSS 隔离或命名空间避免样式冲突。

### 3. 路由冲突
合理规划主子应用的路由结构。

### 4. 通信问题
确保事件名称的一致性，避免内存泄漏。

## 部署说明

### 1. 静态资源部署
- 主应用和子应用分别部署
- 配置正确的资源路径
- 确保跨域访问权限

### 2. 环境配置
```javascript
// 环境变量配置
const config = {
  development: {
    subApp1Url: 'http://localhost:3001',
    subApp2Url: 'http://localhost:3002'
  },
  production: {
    subApp1Url: 'https://sub-app-1.example.com',
    subApp2Url: 'https://sub-app-2.example.com'
  }
}
```
