const fs = require("fs");
const path = require("path");

function deleteEmptyMapFiles(dir) {
  if (!fs.existsSync(dir)) return;
  const files = fs.readdirSync(dir);
  for (const file of files) {
    const fullPath = path.join(dir, file);
    if (fs.statSync(fullPath).isDirectory()) {
      deleteEmptyMapFiles(fullPath);
    } else if (fullPath.endsWith(".map") && fs.statSync(fullPath).size === 0) {
      console.log("Deleting broken .map:", fullPath);
      fs.unlinkSync(fullPath);
    }
  }
}

deleteEmptyMapFiles("./node_modules/element-plus");
