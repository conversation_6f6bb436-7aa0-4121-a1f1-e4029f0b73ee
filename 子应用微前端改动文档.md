# 子应用微前端改动文档

## 主要改动

### 0. __webpack_public_path__ 路径调整
```javascript
if (window.__POWERED_BY_WUJIE__) {
  __webpack_public_path__ = window.__WUJIE_PUBLIC_PATH__;
}
```
**改动说明：**
- 需要将此代码放到main.js首行
- 可以通过import的形式或者直接写代码在首行


### 1. 环境检测与路由配置

```javascript
const wujieEnv = window.__POWERED_BY_WUJIE__

// 子路由实例
sonRouter = new VueRouter({
  base: wujieEnv ? '/D01/' : '/',  // 微前端环境下使用特定base路径
  mode: 'history',
  routes: omsRoutes
})
```

**改动说明：**
- 通过 `window.__POWERED_BY_WUJIE__` 检测是否运行在微前端环境
- 微前端环境下路由base设置为 `/D01/`，独立运行时为 `/`

### 2. Cookie代理机制

```javascript
if (wujieEnv) {
  const { getMainCookie } = window.$wujie.props
  
  // 重写Cookie API，代理到主应用
  Cookies.set = function(name, value, options) {
    getMainCookie().set(name, value, options)
  }
  Cookies.get = function(name, options) {
    return getMainCookie().get(name, options)
  }
  Cookies.remove = function(name, options) {
    getMainCookie().remove(name, options)
  }
}
```

**改动说明：**
- 在微前端环境下，重写Cookie操作方法
- 所有Cookie操作都代理到主应用，确保数据一致性
- 解决子应用domain与主应用不一致的问题

### 3. 生命周期钩子

#### 挂载钩子 (`__WUJIE_MOUNT`)

```javascript
window.__WUJIE_MOUNT = () => {
  console.log('init micro 子应用')
  
  // 1. 主路由代理
  if (getMainRouter) {
    Vue.prototype.$mainRouter = getMainRouter()
  }
  
  // 2. 用户信息同步
  if (getUserInfo) {
    const userInfo = getUserInfo()
    store.commit('user/SET_USERINFO', userInfo)
    localStorage.setItem('getInfo', JSON.stringify(userInfo))
    setToken(userInfo.token)
  }
  
  // 3. 刷新令牌设置
  if (getRefreshToken) {
    setRefreshToken(getRefreshToken())
  }
  
  // 4. 租户信息同步
  if (getTenantinfo) {
    store.commit('user/SET_TENANTINFO', getTenantinfo())
  }
  
  // 5. 标题同步
  if (setDocumentTitle) {
    sonRouter.afterEach((to) => {
      if (to.meta?.title) {
        setDocumentTitle(to.meta.title)
      }
    })
  }
  
  // 6. 应用配置
  applyConfig(getAppConfig())
  
  // 7. Vue实例创建
  instance = new Vue({
    router: sonRouter,
    store,
    i18n,
    render: (h) => h(App)
  }).$mount('#app')
}
```

#### 卸载钩子 (`__WUJIE_UNMOUNT`)

```javascript
window.__WUJIE_UNMOUNT = () => {
  if (instance) {
    instance.$destroy()
    instance.$el.innerHTML = ''
    instance = null
  }
  if (sonRouter) {
    sonRouter = null
  }
}
```

**改动说明：**
- 挂载时同步主应用的用户信息、配置等数据
- 设置路由标题同步机制
- 卸载时清理Vue实例和路由，防止内存泄漏

### 4. 配置应用函数

```javascript
function applyConfig(config) {
  if (config.env !== 'dev') {
    window.console.log = () => {}  // 生产环境禁用console.log
  }
  const baseURL = config.baseURL || window.location.origin + '/'
  Cookies.set('baseApi', baseURL, {
    expires: 5,
    domain: window.location.hostname
  })
  store.dispatch('app/setconfig', config)
}
```

**改动说明：**
- 设置API基础URL到Cookie
- 将配置信息存储到Vuex

### 5. 独立运行模式

```javascript
} else {
  // 非微前端环境，独立运行
  axios
    .get('/appconfig.json')
    .then(({ data }) => {
      applyConfig(data)
      new Vue({
        router: sonRouter,
        store,
        i18n,
        render: (h) => h(App)
      }).$mount('#app')
    })
    .catch((err) => {
      console.error('获取 appconfig.json 失败', err)
      // 配置加载失败时仍然启动应用
      new Vue({
        router: sonRouter,
        store,
        i18n,
        render: (h) => h(App)
      }).$mount('#app')
    })
}
```

**改动说明：**
- 独立运行时从 `/appconfig.json` 加载配置
- 配置加载失败时提供降级方案，确保应用正常启动

