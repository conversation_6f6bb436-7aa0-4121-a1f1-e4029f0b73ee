apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ui-omsweb-fw
  name: ui-omsweb-fw
  namespace: inksoms #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: ui-omsweb-fw
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: ui-omsweb-fw
    spec:
      volumes:
        - name: volume-conf
          configMap:
            name: ui-omsweb-fw-conf
            items:
              - key: appconfig.json
                path: appconfig.json
            defaultMode: 420
      imagePullSecrets:
        - name: aliyun-docker-hub #提前在项目下配置访问阿里云的账号密码
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/ui-omsweb-fw:SNAPSHOT-$BUILD_NUMBER
          imagePullPolicy: Always
          name: ui-omsweb-fw
          ports:
            - containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: 300m
              memory: 600Mi
          volumeMounts:
            - name: volume-conf
              readOnly: true
              mountPath: /home/<USER>/projects/ui-omsweb/appconfig.json
              subPath: appconfig.json
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: ui-omsweb-fw
  name: ui-omsweb-fw
  namespace: inksoms
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
      nodePort: 31100
  selector:
    app: ui-omsweb-fw
  sessionAffinity: None
  type: NodePort
