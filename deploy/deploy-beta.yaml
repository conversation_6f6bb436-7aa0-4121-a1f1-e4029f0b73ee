apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ui-oneweb-fw
  name: ui-oneweb-fw
  namespace: inksbeta #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: ui-oneweb-fw
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: ui-oneweb-fw
    spec:
      volumes:
        - name: volume-conf
          configMap:
            name: ui-oneweb-fw-conf
            items:
              - key: appconfig.json
                path: appconfig.json
            defaultMode: 420
      imagePullSecrets:
        - name: aliyun-docker-hub #提前在项目下配置访问阿里云的账号密码
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/ui-oneweb-fw:SNAPSHOT-$BUILD_NUMBER
          imagePullPolicy: Always
          name: ui-oneweb-fw
          ports:
            - containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: 300m
              memory: 600Mi
          volumeMounts:
            - name: volume-conf
              readOnly: true
              mountPath: /home/<USER>/projects/ui-omsweb/appconfig.json
              subPath: appconfig.json
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: ui-oneweb-fw
  name: ui-oneweb-fw
  namespace: inksbeta
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
      nodePort: 30557
  selector:
    app: ui-oneweb-fw
  sessionAffinity: None
  type: NodePort
