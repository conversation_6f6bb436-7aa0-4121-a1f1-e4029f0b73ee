apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: mf-omsweb
  name: mf-omsweb
  namespace: inksdev-mf #一定要写名称空间
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: mf-omsweb
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: mf-omsweb
    spec:
      imagePullSecrets:
        - name: inks-docker-auth #提前在项目下配置访问阿里云的账号密码
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/mf-omsweb:vue3-$BUILD_NUMBER
          imagePullPolicy: Always
          name: mf-omsweb
          ports:
            - containerPort: 80
              protocol: TCP
          env: #appconfig模板，网址和数字需加引号转字符
            - name: BASE_URL
              value: 'http://dev.inksyun.com:31080'
            - name: SYSTEM_TITLE
            - name: SYSTEM_EULA
              value: inkstech
            - name: SYSTEM_TITLE
              value: 应凯订单管理系统dev
            - name: SYSTEM_CODE
              value: inksoms
            - name: DEPLOY_MODEL
              value: standalone
            - name: DEPLOY_ENV
              value: dev
            - name: LOGIN_IMG
              value: 'https://git.inksyun.com/pool/team-image-bed/-/raw/main/eric/2025/07/2_153947_login-image.bcf811c5.jpg'
            - name: SYSTEM_ICO
              value: 'https://git.inksyun.com/pool/team-image-bed/-/raw/main/eric/2025/07/7_1546_favicon.ico'
            - name: LOCAL_MARK
              value: '0'
          resources:
            limits:
              cpu: 300m
              memory: 600Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: mf-omsweb
  name: mf-omsweb
  namespace: inksdev-mf
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
      nodePort: 30102
  selector:
    app: mf-omsweb
  sessionAffinity: None
  type: NodePort
