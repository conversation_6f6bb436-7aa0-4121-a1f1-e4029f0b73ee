# Wujie Vue 子应用开发文档

## 项目概述

本文档专门介绍如何开发和配置基于 Vue 的 wujie 微前端子应用，包括 Vue 2 和 Vue 3 的完整接入方案。

## 技术栈

- **前端框架**: Vue 2/3
- **构建工具**: Vite / Webpack
- **微前端框架**: wujie
- **路由**: Vue Router
- **状态管理**: Vuex / Pinia


## Vue 3 子应用配置


### 1. 入口文件配置 (src/main.js)

```javascript
import { createApp } from 'vue'
import { bus } from 'wujie-vue3'
import App from './App.vue'
import router from './router'
import store from './store'

let app

// wujie 生命周期钩子
window.__WUJIE_MOUNT = () => {
  app = createApp(App)
  app.use(router)
  app.use(store)
  
  // 注册事件总线
  app.config.globalProperties.$bus = bus
  
  // 挂载应用
  app.mount('#app')
}

window.__WUJIE_UNMOUNT = () => {
  app?.unmount()
  app = null
}

// 独立运行时的逻辑
if (!window.__POWERED_BY_WUJIE__) {
  window.__WUJIE_MOUNT()
}
```

### 2. 路由配置 (src/router/index.js)

```javascript
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/about',
    name: 'About', 
    component: () => import('../views/About.vue')
  }
]

const router = createRouter({
  // 在wujie环境中使用相对路径
  history: createWebHistory(window.__POWERED_BY_WUJIE__ ? '/sub-app/' : '/'),
  routes
})

export default router
```

### 4. Vite 配置 (vite.config.js)

```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3001,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
    }
  },
  build: {
    target: 'es2015',
    lib: {
      entry: 'src/main.js',
      name: 'SubApp',
      formats: ['umd']
    },
    rollupOptions: {
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue'
        }
      }
    }
  },
  define: {
    __VUE_PROD_DEVTOOLS__: false
  }
})
```

## Vue 2 子应用配置

### 1. 入口文件配置 (src/main.js)

```javascript
import Vue from 'vue'
import { bus } from 'wujie-vue2'
import App from './App.vue'
import router from './router'
import store from './store'

Vue.config.productionTip = false

let app

// wujie 生命周期钩子
window.__WUJIE_MOUNT = () => {
  app = new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount('#app')

  // 注册事件总线
  Vue.prototype.$bus = bus
}

window.__WUJIE_UNMOUNT = () => {
  app?.$destroy()
  app = null
}

// 独立运行时的逻辑
if (!window.__POWERED_BY_WUJIE__) {
  window.__WUJIE_MOUNT()
}
```

### 2. Vue 2 路由配置

```javascript
import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('../views/About.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: window.__POWERED_BY_WUJIE__ ? '/sub-app/' : '/',
  routes
})

export default router
```

## 通信机制

### 1. Vue 3 组合式 API 通信

```javascript
// Vue 3 Composition API
import { onMounted, onUnmounted, ref } from 'vue'
import { bus } from 'wujie-vue3'

export default {
  setup() {
    const userInfo = ref({})
    const theme = ref('light')

    const handleMainData = (data) => {
      console.log('收到主应用数据:', data)
      switch(data.type) {
        case 'USER_INFO_UPDATE':
          userInfo.value = data.data
          break
        case 'THEME_CHANGE':
          theme.value = data.data.theme
          break
      }
    }

    onMounted(() => {
      // 监听主应用消息
      bus.$on('main-to-sub', handleMainData)

      // 获取主应用传递的 props
      const props = window.$wujie?.props || {}
      console.log('主应用props:', props)

      // 向主应用发送就绪消息
      bus.$emit('sub-to-main', {
        type: 'SUB_APP_READY',
        data: { appName: 'vue3-sub-app' }
      })
    })

    onUnmounted(() => {
      bus.$off('main-to-sub', handleMainData)
    })

    return {
      userInfo,
      theme
    }
  }
}
```

### 2. Vue 2 选项式 API 通信

```javascript
// Vue 2 Options API
export default {
  data() {
    return {
      userInfo: {},
      theme: 'light'
    }
  },

  mounted() {
    // 监听主应用消息
    this.$bus.$on('main-to-sub', this.handleMainData)

    // 获取主应用传递的 props
    const props = window.$wujie?.props || {}
    console.log('主应用props:', props)

    // 向主应用发送就绪消息
    this.$bus.$emit('sub-to-main', {
      type: 'SUB_APP_READY',
      data: { appName: 'vue2-sub-app' }
    })
  },

  beforeDestroy() {
    this.$bus.$off('main-to-sub', this.handleMainData)
  },

  methods: {
    handleMainData(data) {
      console.log('收到主应用数据:', data)
      switch(data.type) {
        case 'USER_INFO_UPDATE':
          this.userInfo = data.data
          break
        case 'THEME_CHANGE':
          this.theme = data.data.theme
          break
      }
    }
  }
}
```

### 3. 向主应用发送数据

```javascript
// Vue 3 Composition API
import { getCurrentInstance } from 'vue'
import { bus } from 'wujie-vue3'

export default {
  setup() {
    const instance = getCurrentInstance()

    const sendToMain = (type, data) => {
      bus.$emit('sub-to-main', {
        type,
        data,
        timestamp: Date.now()
      })
    }

    const handleRouteChange = () => {
      sendToMain('ROUTE_CHANGE', {
        path: instance.proxy.$route.path,
        title: document.title
      })
    }

    return {
      sendToMain,
      handleRouteChange
    }
  }
}

// Vue 2 Options API
export default {
  methods: {
    sendToMain(type, data) {
      this.$bus.$emit('sub-to-main', {
        type,
        data,
        timestamp: Date.now()
      })
    },

    handleRouteChange() {
      this.sendToMain('ROUTE_CHANGE', {
        path: this.$route.path,
        title: document.title
      })
    }
  }
}
```

### 4. 跨应用状态共享

```javascript
// Vue 3 + Pinia
import { defineStore } from 'pinia'
import { bus } from 'wujie-vue3'

export const useMainStore = defineStore('main', {
  state: () => ({
    userInfo: {},
    sharedData: {}
  }),

  actions: {
    // 同步主应用状态
    syncMainState() {
      const mainStore = window.$wujie?.store
      if (mainStore) {
        this.userInfo = mainStore.state.user.info
        this.sharedData = mainStore.state.shared
      }
    },

    // 更新主应用状态
    updateMainState(payload) {
      bus.$emit('sub-to-main', {
        type: 'UPDATE_STORE',
        payload
      })
    }
  }
})

// Vue 2 + Vuex
export default {
  computed: {
    mainUserInfo() {
      const mainStore = window.$wujie?.store
      return mainStore?.state.user.info || {}
    }
  },

  methods: {
    updateMainStore(payload) {
      this.$bus.$emit('sub-to-main', {
        type: 'UPDATE_STORE',
        payload
      })
    }
  }
}
```

## 样式隔离

### 1. CSS Modules

```vue
<template>
  <div :class="$style.container">
    <h1 :class="$style.title">子应用标题</h1>
  </div>
</template>

<style module>
.container {
  padding: 20px;
  background: #f5f5f5;
}

.title {
  color: #333;
  font-size: 24px;
}
</style>
```

### 2. Scoped 样式

```vue
<style scoped>
.container {
  /* 样式只作用于当前组件 */
  padding: 20px;
}
</style>
```

### 3. 命名空间

```css
/* 使用应用前缀 */
.sub-app-container {
  padding: 20px;
}

.sub-app-title {
  font-size: 24px;
}
```

## 环境检测

### 1. 检测运行环境

```javascript
// 检测是否在 wujie 环境中运行
const isWujieEnv = window.__POWERED_BY_WUJIE__

// 检测是否为独立运行
const isStandalone = !isWujieEnv

// 获取应用名称
const appName = window.__WUJIE_PUBLIC_PATH__?.split('/').filter(Boolean).pop()

console.log('运行环境:', {
  isWujieEnv,
  isStandalone,
  appName
})
```

### 2. Vue 3 条件渲染

```vue
<template>
  <div>
    <!-- 只在独立运行时显示 -->
    <header v-if="isStandalone">
      <nav>
        <router-link to="/">首页</router-link>
        <router-link to="/about">关于</router-link>
      </nav>
    </header>

    <!-- 主要内容 -->
    <main>
      <router-view />
    </main>

    <!-- 只在wujie环境中显示 -->
    <footer v-if="isWujieEnv">
      <p>子应用运行在微前端环境中</p>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const isWujieEnv = ref(window.__POWERED_BY_WUJIE__)
const isStandalone = ref(!window.__POWERED_BY_WUJIE__)
const appName = ref('')

onMounted(() => {
  if (isWujieEnv.value) {
    appName.value = window.__WUJIE_PUBLIC_PATH__?.split('/').filter(Boolean).pop() || 'unknown'
  }
})
</script>
```

### 3. Vue 2 条件渲染

```vue
<template>
  <div>
    <!-- 只在独立运行时显示 -->
    <header v-if="isStandalone">
      <nav>
        <router-link to="/">首页</router-link>
        <router-link to="/about">关于</router-link>
      </nav>
    </header>

    <!-- 主要内容 -->
    <main>
      <router-view />
    </main>

    <!-- 只在wujie环境中显示 -->
    <footer v-if="isWujieEnv">
      <p>子应用: {{ appName }}</p>
    </footer>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isWujieEnv: window.__POWERED_BY_WUJIE__,
      isStandalone: !window.__POWERED_BY_WUJIE__,
      appName: ''
    }
  },

  mounted() {
    if (this.isWujieEnv) {
      this.appName = window.__WUJIE_PUBLIC_PATH__?.split('/').filter(Boolean).pop() || 'unknown'
    }
  }
}
</script>
```

## 调试技巧

### 1. 开发环境调试

```javascript
// 开启调试模式
if (process.env.NODE_ENV === 'development') {
  window.__SUB_APP_DEBUG__ = true
  
  // 调试信息
  console.log('子应用调试信息:', {
    wujieEnv: window.__POWERED_BY_WUJIE__,
    props: window.$wujie?.props,
    publicPath: window.__WUJIE_PUBLIC_PATH__
  })
}
```

### 2. Vue 通信调试

```javascript
// Vue 3 调试 Mixin
import { bus } from 'wujie-vue3'

export const debugMixin = {
  mounted() {
    if (process.env.NODE_ENV === 'development') {
      // 监听所有通信消息
      bus.$on('*', this.logCommunication)
    }
  },

  beforeUnmount() {
    if (process.env.NODE_ENV === 'development') {
      bus.$off('*', this.logCommunication)
    }
  },

  methods: {
    logCommunication(eventName, data) {
      console.log(`[Vue子应用通信] 事件: ${eventName}`, data)
    },

    sendDebugMessage(type, data) {
      if (window.__SUB_APP_DEBUG__) {
        bus.$emit('sub-to-main', {
          type: 'DEBUG_MESSAGE',
          subType: type,
          data,
          component: this.$options.name || 'Unknown',
          timestamp: Date.now()
        })
      }
    }
  }
}

// Vue 2 全局调试
Vue.mixin({
  mounted() {
    if (process.env.NODE_ENV === 'development' && this.$options.name) {
      console.log(`[Vue组件] ${this.$options.name} 已挂载`)
    }
  }
})
```

## 性能优化

### 1. Vue 路由懒加载

```javascript
// Vue 3 路由懒加载
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/heavy-page',
    name: 'HeavyPage',
    component: () => import(/* webpackChunkName: "heavy" */ '../views/HeavyPage.vue')
  },
  {
    path: '/user',
    name: 'User',
    component: () => import(/* webpackChunkName: "user" */ '../views/User.vue'),
    // 路由级别的代码分割
    meta: { requiresAuth: true }
  }
]

// Vue 2 路由懒加载
const routes = [
  {
    path: '/heavy-page',
    component: () => import(/* webpackChunkName: "heavy" */ '../views/HeavyPage.vue')
  }
]
```

### 2. Vue 组件懒加载

```vue
<template>
  <div>
    <!-- 条件渲染重组件 -->
    <heavy-component v-if="showHeavyComponent" />
    <button @click="loadHeavyComponent">加载重组件</button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      showHeavyComponent: false,
      HeavyComponent: null
    }
  },

  methods: {
    async loadHeavyComponent() {
      if (!this.HeavyComponent) {
        const { default: HeavyComponent } = await import('../components/HeavyComponent.vue')
        this.$options.components.HeavyComponent = HeavyComponent
      }
      this.showHeavyComponent = true
    }
  }
}
</script>
```

### 3. Vue 内存管理

```javascript
// Vue 3 Composition API
import { onBeforeUnmount, ref } from 'vue'
import { bus } from 'wujie-vue3'

export default {
  setup() {
    const timer = ref(null)
    const eventHandlers = ref([])

    const addEventHandler = (event, handler) => {
      bus.$on(event, handler)
      eventHandlers.value.push({ event, handler })
    }

    onBeforeUnmount(() => {
      // 清理事件监听
      eventHandlers.value.forEach(({ event, handler }) => {
        bus.$off(event, handler)
      })

      // 清理定时器
      if (timer.value) {
        clearInterval(timer.value)
      }

      // 清理引用
      eventHandlers.value = []
    })

    return {
      addEventHandler
    }
  }
}

// Vue 2 Options API
export default {
  beforeDestroy() {
    // 清理事件监听
    this.$bus.$off('main-to-sub', this.handleMainMessage)

    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 清理DOM引用
    this.domRefs = null

    // 清理观察者
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}
```

## 部署配置

### 1. Vue 3 + Vite 构建配置

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],

  build: {
    target: 'es2015',
    lib: {
      entry: 'src/main.js',
      name: 'VueSubApp',
      formats: ['umd']
    },
    rollupOptions: {
      external: ['vue', 'vue-router'],
      output: {
        globals: {
          vue: 'Vue',
          'vue-router': 'VueRouter'
        }
      }
    }
  },

  server: {
    port: 3001,
    cors: true
  }
})
```

### 2. Vue 2 + Webpack 构建配置

```javascript
// webpack.config.js
const { VueLoaderPlugin } = require('vue-loader')

module.exports = {
  entry: './src/main.js',

  output: {
    library: 'Vue2SubApp',
    libraryTarget: 'umd',
    publicPath: process.env.NODE_ENV === 'production'
      ? 'https://cdn.example.com/vue2-sub-app/'
      : '/'
  },

  externals: {
    vue: 'Vue',
    'vue-router': 'VueRouter',
    vuex: 'Vuex'
  },

  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      {
        test: /\.js$/,
        loader: 'babel-loader',
        exclude: /node_modules/
      }
    ]
  },

  plugins: [
    new VueLoaderPlugin()
  ]
}
```

### 2. 环境变量

```javascript
// .env.production
VUE_APP_API_BASE_URL=https://api.example.com
VUE_APP_PUBLIC_PATH=https://cdn.example.com/sub-app/

// .env.development  
VUE_APP_API_BASE_URL=http://localhost:8080
VUE_APP_PUBLIC_PATH=/
```

## 常见问题

### 1. 路由问题
- 确保路由基础路径配置正确
- 处理好 hash 和 history 模式

### 2. 样式冲突
- 使用 CSS 隔离方案
- 避免全局样式污染

### 3. 资源加载
- 配置正确的 publicPath
- 处理跨域资源访问

### 4. 通信异常
- 检查事件名称拼写
- 确保生命周期钩子正确执行
