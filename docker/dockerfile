# 基础镜像
FROM hub.inksyun.com/inksdev/nginx:latest
# author
MAINTAINER omsweb

# 挂载目录
VOLUME /home/<USER>/projects/ui-omsweb
# 创建目录
RUN mkdir -p /home/<USER>/projects/ui-omsweb
# 指定路径
WORKDIR /home/<USER>/projects/ui-omsweb
# 复制conf文件到路径
COPY ./conf/nginx.conf /etc/nginx/nginx.conf
# 复制html文件到路径
COPY ./html/dist /home/<USER>/projects/ui-omsweb
# 增加代码
COPY ./conf/appconfig.template.json /home/<USER>/projects/ui-omsweb/appconfig.template.json
# 启动脚本
COPY ./entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/entrypoint.sh
ENTRYPOINT ["entrypoint.sh"]
