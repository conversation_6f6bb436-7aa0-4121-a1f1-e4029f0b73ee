<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="OMS订单管理系统是一款针对制造企业设计的综合性订单管理解决方案，致力于帮助企业优化生产流程、提高工作效率、降低运营成本，实现可持续发展。作为一款功能类似ERP的系统，OMS涵盖了订单录入、生产计划、物料采购、库存管理、销售分析等全方位的业务管理模块，使得企业能够更加精细化、智能化地掌控订单全过程。"
    />
    <meta name="keywords" content="OMS,ERP,订单管理" />
    <title>应凯订单管理系统</title>
  </head>

  <body>
    <div id="app">
      <div class="loader"></div>
    </div>
  </body>
  <script type="module" src="/src/main.ts"></script>

  <style>
    html,
    body,
    #app {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .loader {
      --d: 22px;

      width: 4px;
      height: 4px;
      color: #25b09b;
      border-radius: 50%;
      box-shadow:
        calc(1 * var(--d)) calc(0 * var(--d)) 0 0,
        calc(0.707 * var(--d)) calc(0.707 * var(--d)) 0 1px,
        calc(0 * var(--d)) calc(1 * var(--d)) 0 2px,
        calc(-0.707 * var(--d)) calc(0.707 * var(--d)) 0 3px,
        calc(-1 * var(--d)) calc(0 * var(--d)) 0 4px,
        calc(-0.707 * var(--d)) calc(-0.707 * var(--d)) 0 5px,
        calc(0 * var(--d)) calc(-1 * var(--d)) 0 6px;
      animation: l27 1s infinite steps(8);
    }

    @keyframes l27 {
      100% {
        transform: rotate(1turn);
      }
    }
  </style>
</html>
